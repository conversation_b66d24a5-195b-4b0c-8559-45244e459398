{"name": "rc-cascader", "version": "3.34.0", "description": "cascade select ui component for react", "keywords": ["react", "react-component", "react-cascader", "react-select", "select", "cascade", "cascader"], "homepage": "https://github.com/react-component/cascader", "bugs": {"url": "https://github.com/react-component/cascader/issues"}, "repository": {"type": "git", "url": "https://github.com/react-component/cascader.git"}, "license": "MIT", "author": "<EMAIL>", "main": "./lib/index", "module": "./es/index", "files": ["lib", "es", "assets/*.css", "assets/*.less"], "scripts": {"build": "dumi build", "compile": "father build", "coverage": "father test --coverage", "deploy": "UMI_ENV=gh npm run build && gh-pages -d dist", "lint": "eslint src/ examples/ --ext .tsx,.ts,.jsx,.jsx", "now-build": "npm run build", "prepublishOnly": "npm run compile && np --no-cleanup --yolo --no-publish --any-branch", "lint:tsc": "tsc -p tsconfig.json --noEmit", "start": "dumi dev", "test": "rc-test"}, "dependencies": {"@babel/runtime": "^7.25.7", "classnames": "^2.3.1", "rc-select": "~14.16.2", "rc-tree": "~5.13.0", "rc-util": "^5.43.0"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.0", "@rc-component/trigger": "^1.5.0", "@testing-library/react": "^12.1.5", "@types/classnames": "^2.2.6", "@types/enzyme": "^3.1.15", "@types/jest": "^29.4.0", "@types/react": "^17.0.38", "@types/react-dom": "^18.0.11", "@types/warning": "^3.0.0", "@umijs/fabric": "^4.0.0", "array-tree-filter": "^3.0.2", "cheerio": "1.0.0-rc.12", "cross-env": "^7.0.0", "dumi": "^2.1.10", "enzyme": "^3.3.0", "enzyme-adapter-react-16": "^1.15.6", "enzyme-to-json": "^3.2.1", "eslint": "^8.54.0", "eslint-plugin-jest": "^28.8.3", "eslint-plugin-unicorn": "^52.0.0", "father": "^4.0.0", "gh-pages": "^6.1.1", "glob": "^7.1.6", "less": "^4.2.0", "np": "^10.0.2", "prettier": "^3.1.0", "rc-field-form": "^1.44.0", "rc-test": "^7.0.14", "react": "^16.0.0", "react-dom": "^16.0.0", "typescript": "^5.3.2"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}