# 开发规范和最佳实践

## 项目结构与文件管理

1. **现代化框架初始化**
   - 使用成熟的开发框架（如React + Vite、Next.js等）进行项目初始化
   - 不要在根目录随意创建文件

2. **目录结构规范**
   - 严格按照项目架构将文件放置在正确的目录结构中
   - 标准目录：src/、components/、utils/、tests/等

3. **依赖管理**
   - 使用适当的包管理器（npm、yarn、pnpm等）管理依赖
   - 避免手动编辑配置文件

## 问题解决策略

4. **技术难题处理流程**
   - 首先尝试标准解决方案
   - 如果错误无法直接解决，需要：
     * 分析问题对整体项目的影响
     * 提供2-3个可行的替代方案
     * 详细说明每个方案的优缺点
     * 等待用户确认后再执行，避免擅自绕过问题

## 任务管理与开发流程

5. **复杂功能任务管理**
   - 使用任务管理工具进行规划和跟踪
   - 将大功能拆分为独立的子任务（每个任务约20分钟完工）
   - 每完成一个任务后进行单元测试
   - 记录任务的功能作用和与其他模块的接口方式
   - 及时更新任务状态和进度

## 代码质量与维护

6. **代码模块化**
   - 代码文件按功能模块拆分
   - 单个文件控制在合理长度内（建议不超过500行）

7. **单元测试**
   - 为每个功能模块编写相应的单元测试

8. **代码规范**
   - 使用清晰的命名规范和适当的注释
   - 定期进行代码重构，保持代码整洁

## 其他开发规范

9. **版本控制**
   - 每个功能完成后进行有意义的提交
   - 编写清晰的提交信息

10. **错误处理**
    - 实现完善的错误处理机制
    - 包括用户友好的错误提示

11. **性能优化**
    - 考虑代码性能
    - 避免不必要的重复计算和资源浪费

12. **安全性**
    - 遵循安全编码规范
    - 验证用户输入，防止常见安全漏洞

13. **文档编写**
    - 为重要功能编写技术文档和使用说明

14. **兼容性**
    - 确保代码在目标环境中的兼容性和稳定性

15. **持续改进**
    - 定期审查和更新开发规范
    - 根据项目需求调整最佳实践
