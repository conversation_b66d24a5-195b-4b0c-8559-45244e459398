import { create } from 'zustand'
import type { Project, ExhibitionPlan } from '@/types'
import { mockApi } from '@/services/mockApi'

interface ProjectState {
  projects: Project[]
  currentProject: Project | null
  currentPlan: ExhibitionPlan | null
  isLoading: boolean
  statistics: any
}

interface ProjectActions {
  fetchProjects: () => Promise<void>
  createProject: (projectData: Partial<Project>) => Promise<Project | null>
  setCurrentProject: (project: Project | null) => void
  generatePlan: (projectId: string, requirements: any) => Promise<ExhibitionPlan | null>
  setCurrentPlan: (plan: ExhibitionPlan | null) => void
  fetchStatistics: () => Promise<void>
  setLoading: (loading: boolean) => void
}

type ProjectStore = ProjectState & ProjectActions

export const useProjectStore = create<ProjectStore>((set, get) => ({
  // 初始状态
  projects: [],
  currentProject: null,
  currentPlan: null,
  isLoading: false,
  statistics: null,

  // 获取项目列表
  fetchProjects: async () => {
    set({ isLoading: true })
    try {
      const response = await mockApi.getProjects()
      if (response.success && response.data) {
        set({ projects: response.data, isLoading: false })
      } else {
        set({ isLoading: false })
      }
    } catch (error) {
      set({ isLoading: false })
    }
  },

  // 创建项目
  createProject: async (projectData: Partial<Project>) => {
    set({ isLoading: true })
    try {
      const response = await mockApi.createProject(projectData)
      if (response.success && response.data) {
        const { projects } = get()
        set({
          projects: [...projects, response.data],
          currentProject: response.data,
          isLoading: false
        })
        return response.data
      } else {
        set({ isLoading: false })
        return null
      }
    } catch (error) {
      set({ isLoading: false })
      return null
    }
  },

  // 设置当前项目
  setCurrentProject: (project: Project | null) => {
    set({ currentProject: project })
  },

  // 生成AI方案
  generatePlan: async (projectId: string, requirements: any) => {
    set({ isLoading: true })
    try {
      const response = await mockApi.generatePlan(projectId, requirements)
      if (response.success && response.data) {
        set({
          currentPlan: response.data,
          isLoading: false
        })
        return response.data
      } else {
        set({ isLoading: false })
        return null
      }
    } catch (error) {
      set({ isLoading: false })
      return null
    }
  },

  // 设置当前方案
  setCurrentPlan: (plan: ExhibitionPlan | null) => {
    set({ currentPlan: plan })
  },

  // 获取统计数据
  fetchStatistics: async () => {
    try {
      const response = await mockApi.getStatistics()
      if (response.success && response.data) {
        set({ statistics: response.data })
      }
    } catch (error) {
      console.error('Failed to fetch statistics:', error)
    }
  },

  // 设置加载状态
  setLoading: (loading: boolean) => {
    set({ isLoading: loading })
  }
}))
