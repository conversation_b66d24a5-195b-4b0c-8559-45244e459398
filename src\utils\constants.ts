// API配置
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'
export const AI_SERVICE_URL = import.meta.env.VITE_AI_SERVICE_URL || 'http://localhost:8000'

// 应用配置
export const APP_CONFIG = {
  name: 'MCTT会展实训平台',
  version: '1.0.0',
  description: '会展行业虚实融合实训教学平台',
  author: 'MCTT Team',
}

// 路由路径
export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  REGISTER: '/register',
  DASHBOARD: '/dashboard',
  PROJECTS: '/projects',
  AI_PLAN: '/ai-plan',
  RENDER_2D: '/render-2d',
  MODEL_3D: '/model-3d',
  CONSTRUCTION: '/construction',
  MONITORING: '/monitoring',
  PROFILE: '/profile',
} as const

// 用户角色权限
export const ROLE_PERMISSIONS = {
  student: [
    'view_projects',
    'create_project',
    'generate_plan',
    'render_2d',
    'view_3d',
  ],
  teacher: [
    'view_projects',
    'create_project',
    'generate_plan',
    'render_2d',
    'view_3d',
    'monitor_students',
    'grade_projects',
    'manage_class',
  ],
  enterprise: [
    'view_projects',
    'create_project',
    'generate_plan',
    'render_2d',
    'view_3d',
    'construction_management',
    'export_data',
  ],
  admin: [
    'all_permissions',
  ],
} as const

// 文件上传配置
export const UPLOAD_CONFIG = {
  maxSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
  imageTypes: ['image/jpeg', 'image/png', 'image/gif'],
}

// 渲染配置
export const RENDER_CONFIG = {
  defaultResolution: {
    width: 1920,
    height: 1080,
  },
  supportedFormats: ['png', 'jpg', 'pdf'],
  maxRenderTime: 300, // 5分钟
}

// 3D模型配置
export const MODEL_3D_CONFIG = {
  defaultCamera: {
    position: [10, 10, 10],
    target: [0, 0, 0],
  },
  controls: {
    enableZoom: true,
    enablePan: true,
    enableRotate: true,
  },
}

// 主题配置
export const THEME_CONFIG = {
  primaryColor: '#1890ff',
  successColor: '#52c41a',
  warningColor: '#faad14',
  errorColor: '#f5222d',
  borderRadius: 6,
}

// 分页配置
export const PAGINATION_CONFIG = {
  defaultPageSize: 10,
  pageSizeOptions: ['10', '20', '50', '100'],
  showSizeChanger: true,
  showQuickJumper: true,
}

// 本地存储键名
export const STORAGE_KEYS = {
  TOKEN: 'mctt_token',
  USER: 'mctt_user',
  THEME: 'mctt_theme',
  LANGUAGE: 'mctt_language',
} as const

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  UNAUTHORIZED: '登录已过期，请重新登录',
  FORBIDDEN: '权限不足，无法访问该资源',
  NOT_FOUND: '请求的资源不存在',
  SERVER_ERROR: '服务器内部错误，请稍后重试',
  VALIDATION_ERROR: '输入数据格式错误',
  UPLOAD_ERROR: '文件上传失败',
  RENDER_ERROR: '渲染失败，请重试',
} as const

// 成功消息
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: '登录成功',
  LOGOUT_SUCCESS: '退出成功',
  SAVE_SUCCESS: '保存成功',
  DELETE_SUCCESS: '删除成功',
  UPDATE_SUCCESS: '更新成功',
  UPLOAD_SUCCESS: '上传成功',
  RENDER_SUCCESS: '渲染完成',
} as const
