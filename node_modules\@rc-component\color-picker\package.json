{"name": "@rc-component/color-picker", "version": "2.0.1", "description": "React Color Picker", "keywords": ["react", "react-component", "react-mentions", "color-picker"], "homepage": "http://github.com/react-component/color-picker", "bugs": {"url": "http://github.com/react-component/color-picker/issues"}, "repository": {"type": "git", "url": "**************:react-component/color-picker.git"}, "license": "MIT", "main": "./lib/index", "module": "./es/index", "files": ["assets", "es", "lib"], "scripts": {"compile": "father build && lessc assets/index.less assets/index.css", "coverage": "vitest run --coverage", "docs:build": "dumi build", "docs:deploy": "gh-pages -d .doc", "docs:preview": "PREVIEW=true dumi build", "gh-pages": "npm run docs:build && npm run docs:deploy", "lint": "eslint src/ --ext .tsx,.ts", "prepare": "husky install", "prepublishOnly": "npm run compile && np --yolo --no-publish", "postpublish": "npm run gh-pages", "start": "dumi dev", "test": "vitest run"}, "lint-staged": {"**/*.{js,jsx,tsx,ts,md,json}": ["prettier --write", "git add"]}, "dependencies": {"@ant-design/fast-color": "^2.0.6", "@babel/runtime": "^7.23.6", "classnames": "^2.2.6", "rc-util": "^5.38.1"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.0", "@rc-component/trigger": "^1.13.0", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@types/classnames": "^2.2.6", "@types/react": "^18.0.8", "@types/react-dom": "^18.0.3", "@types/warning": "^3.0.0", "@umijs/fabric": "^3.0.0", "@vitest/coverage-c8": "^0.31.0", "dumi": "^2.0.18", "eslint": "^8.54.0", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-unicorn": "^49.0.0", "father": "^4.0.0", "gh-pages": "^4.0.0", "husky": "^8.0.3", "jsdom": "^22.0.0", "less": "^4.2.0", "lint-staged": "^13.1.0", "np": "^7.0.0", "prettier": "^2.0.5", "react": "^18.0.0", "react-dom": "^18.0.0", "typescript": "^5.1.3", "vitest": "^0.31.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}