{"version": 3, "sources": ["webpack://Slider/webpack/universalModuleDefinition", "webpack://Slider/webpack/bootstrap", "webpack://Slider/./src/index.js", "webpack://Slider/./src/slider.js", "webpack://Slider/./node_modules/_@babel_runtime@7.24.1@@babel/runtime/helpers/extends.js", "webpack://Slider/./node_modules/_@babel_runtime@7.24.1@@babel/runtime/helpers/objectSpread2.js", "webpack://Slider/./node_modules/_@babel_runtime@7.24.1@@babel/runtime/helpers/defineProperty.js", "webpack://Slider/./node_modules/_@babel_runtime@7.24.1@@babel/runtime/helpers/toPropertyKey.js", "webpack://Slider/./node_modules/_@babel_runtime@7.24.1@@babel/runtime/helpers/typeof.js", "webpack://Slider/./node_modules/_@babel_runtime@7.24.1@@babel/runtime/helpers/toPrimitive.js", "webpack://Slider/./node_modules/_@babel_runtime@7.24.1@@babel/runtime/helpers/classCallCheck.js", "webpack://Slider/./node_modules/_@babel_runtime@7.24.1@@babel/runtime/helpers/createClass.js", "webpack://Slider/./node_modules/_@babel_runtime@7.24.1@@babel/runtime/helpers/possibleConstructorReturn.js", "webpack://Slider/./node_modules/_@babel_runtime@7.24.1@@babel/runtime/helpers/assertThisInitialized.js", "webpack://Slider/./node_modules/_@babel_runtime@7.24.1@@babel/runtime/helpers/isNativeReflectConstruct.js", "webpack://Slider/./node_modules/_@babel_runtime@7.24.1@@babel/runtime/helpers/getPrototypeOf.js", "webpack://Slider/./node_modules/_@babel_runtime@7.24.1@@babel/runtime/helpers/inherits.js", "webpack://Slider/./node_modules/_@babel_runtime@7.24.1@@babel/runtime/helpers/setPrototypeOf.js", "webpack://Slider/external {\"root\":\"React\",\"commonjs2\":\"react\",\"commonjs\":\"react\",\"amd\":\"react\"}", "webpack://Slider/./src/inner-slider.js", "webpack://Slider/./node_modules/_@babel_runtime@7.24.1@@babel/runtime/helpers/objectWithoutProperties.js", "webpack://Slider/./node_modules/_@babel_runtime@7.24.1@@babel/runtime/helpers/objectWithoutPropertiesLoose.js", "webpack://Slider/./src/initial-state.js", "webpack://Slider/./node_modules/_throttle-debounce@5.0.0@throttle-debounce/esm/index.js", "webpack://Slider/./node_modules/_classnames@2.5.1@classnames/index.js", "webpack://Slider/./src/utils/innerSliderUtils.js", "webpack://Slider/./src/default-props.js", "webpack://Slider/./src/track.js", "webpack://Slider/./src/dots.js", "webpack://Slider/./src/arrows.js", "webpack://Slider/./node_modules/_resize-observer-polyfill@1.5.1@resize-observer-polyfill/dist/ResizeObserver.es.js", "webpack://Slider/(webpack)/buildin/global.js", "webpack://Slider/./node_modules/_json2mq@0.2.0@json2mq/index.js", "webpack://Slider/./node_modules/_string-convert@0.2.1@string-convert/camel2hyphen.js"], "names": ["Slide<PERSON>", "_callSuper", "t", "o", "e", "_getPrototypeOf", "_possibleConstructorReturn", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "_React$Component", "props", "_this", "_classCallCheck", "_defineProperty", "ref", "innerSlider", "slick<PERSON>rev", "slickNext", "slide", "dontAnimate", "arguments", "length", "undefined", "slickGoTo", "pause", "autoPlay", "state", "breakpoint", "_responsiveMediaHandlers", "_inherits", "_createClass", "key", "value", "media", "query", "handler", "mql", "window", "matchMedia", "listener", "_ref", "matches", "addListener", "push", "componentDidMount", "_this2", "responsive", "breakpoints", "map", "breakpt", "sort", "x", "y", "for<PERSON>ach", "index", "b<PERSON><PERSON><PERSON>", "json2mq", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "canUseDOM", "setState", "slice", "componentWillUnmount", "obj", "removeListener", "render", "_this3", "settings", "newProps", "filter", "resp", "_objectSpread", "defaultProps", "centerMode", "slidesToScroll", "process", "console", "warn", "concat", "fade", "slidesToShow", "children", "React", "Children", "toArray", "child", "trim", "variableWidth", "rows", "slidesPerRow", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentWidth", "i", "newSlide", "j", "row", "k", "style", "width", "cloneElement", "tabIndex", "display", "createElement", "className", "infinite", "unslick", "InnerSlider", "_extends", "innerSliderRefHandler", "filterSettings", "Component", "_excluded", "list", "track", "adaptiveHeight", "elem", "querySelector", "currentSlide", "height", "getHeight", "onInit", "lazyLoad", "slidesToLoad", "getOnDemandLazySlides", "prevState", "lazyLoadedList", "onLazyLoad", "spec", "listRef", "trackRef", "updateState", "adaptHeight", "autoplay", "lazyLoadTimer", "setInterval", "progressiveLazyLoad", "ro", "ResizeObserver", "animating", "onWindowResized", "callbackTimers", "setTimeout", "speed", "observe", "document", "querySelectorAll", "Array", "prototype", "call", "onfocus", "pauseOnFocus", "onSlideFocus", "onblur", "onSlideBlur", "addEventListener", "attachEvent", "animationEndCallback", "clearTimeout", "clearInterval", "timer", "removeEventListener", "detachEvent", "autoplayTimer", "disconnect", "prevProps", "checkImagesLoad", "onReInit", "setTrackStyle", "didPropsChange", "count", "changeSlide", "message", "autoplaySpeed", "debouncedResize", "cancel", "debounce", "resizeWindow", "isTrackMounted", "Boolean", "node", "callback", "updatedState", "initializedState", "slideIndex", "targetLeft", "getTrackLeft", "left", "trackStyle", "getTrackCSS", "trackWidth", "trackLeft", "childrenWidths", "preClones", "getPreClones", "slideCount", "postClones", "getPostClones", "childrenCount", "slideWidth", "images", "imagesCount", "loadedCount", "image", "onclick", "parentNode", "focus", "prevClickHandler", "onload", "onerror", "onLazyLoadError", "indexOf", "_this$props", "asNavFor", "beforeChange", "afterChange", "_<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "useCSS", "nextState", "waitForAnimate", "asNavForIndex", "firstBatch", "_objectWithoutProperties", "options", "targetSlide", "focusOnSelect", "nodes", "clickable", "stopPropagation", "preventDefault", "dir", "<PERSON><PERSON><PERSON><PERSON>", "accessibility", "rtl", "event", "returnValue", "ontouchmove", "verticalSwiping", "disableBodyScroll", "swipeStart", "swipe", "draggable", "swipeMove", "swipeEnd", "triggerSlideHandler", "enableBodyScroll", "Number", "isNaN", "nextIndex", "canGoNext", "playType", "autoplaying", "play", "pauseType", "classnames", "vertical", "trackProps", "extractObject", "pauseOnHover", "onMouseEnter", "onTrackOver", "onMouseLeave", "onTrackLeave", "onMouseOver", "<PERSON><PERSON><PERSON><PERSON>", "dots", "dotProps", "pauseOnDotsHover", "clickHandler", "onDotsLeave", "onDotsOver", "Dots", "prevArrow", "nextArrow", "arrowProps", "arrows", "PrevArrow", "NextArrow", "verticalHeightStyle", "listHeight", "centerPaddingStyle", "padding", "centerPadding", "listStyle", "touchMove", "listProps", "onClick", "onMouseDown", "onMouseMove", "dragging", "onMouseUp", "onTouchStart", "onTouchMove", "onTouchEnd", "touchEnd", "onTouchCancel", "onKeyDown", "innerSliderProps", "listRefHandler", "Track", "trackRefHandler", "initialState", "initialSlide", "ssrState", "ssrInit", "_i3", "_Object$keys", "Object", "keys", "hasOwnProperty", "_typeof", "currentDirection", "currentLeft", "direction", "edgeDragged", "initialized", "listWidth", "scrolling", "slideHeight", "swipeLeft", "swiped", "swiping", "touchObject", "startX", "startY", "curX", "curY", "clamp", "number", "lowerBound", "upperBound", "Math", "max", "min", "safePreventDefault", "passiveEvents", "includes", "_reactName", "onDemandSlides", "startIndex", "lazyStartIndex", "endIndex", "lazyEndIndex", "getRequiredLazySlides", "requiredSlides", "lazySlidesOnLeft", "lazySlidesOnRight", "floor", "parseInt", "getWidth", "offsetWidth", "offsetHeight", "getSwipeDirection", "xDist", "yDist", "r", "swipeAngle", "atan2", "round", "PI", "abs", "canGo", "newObject", "listNode", "ceil", "trackNode", "centerPaddingAdj", "animationSlide", "finalSlide", "animationLeft", "finalLeft", "getTrackAnimateCSS", "indexOffset", "previousInt", "slideOffset", "unevenOffset", "previousTargetSlide", "siblingDirection", "target", "tagName", "match", "keyCode", "type", "touches", "pageX", "clientX", "pageY", "clientY", "swipeToSlide", "edgeFriction", "onEdge", "swipeEvent", "curL<PERSON>t", "swipe<PERSON><PERSON><PERSON>", "sqrt", "pow", "verticalSwipeLength", "positionOffset", "dotCount", "swipeDirection", "touchSwipeLength", "touchThreshold", "onSwipe", "minSwipe", "activeSlide", "getSlideCount", "checkNavigable", "getNavigableIndexes", "counter", "indexes", "navigables", "prevNavigable", "n", "centerOffset", "swipedSlide", "slickList", "slides", "from", "every", "offsetLeft", "offsetTop", "currentIndex", "slidesTraversed", "dataset", "checkSpecKeys", "keysArray", "reduce", "error", "trackHeight", "getTotalSlides", "trackChildren", "opacity", "transition", "WebkitTransition", "useTransform", "WebkitTransform", "transform", "msTransform", "marginLeft", "marginTop", "cssEase", "verticalOffset", "slidesToOffset", "targetSlideIndex", "trackElem", "childNodes", "slidesOnRight", "slidesOnLeft", "right", "_ref2", "validSettings", "acc", "<PERSON><PERSON><PERSON>", "appendDots", "customPaging", "dotsClass", "easing", "getSlideClasses", "slickActive", "slickCenter", "slickCloned", "focusedSlide", "<PERSON><PERSON><PERSON><PERSON>", "getSlideStyle", "position", "top", "zIndex", "<PERSON><PERSON><PERSON>", "fallback<PERSON><PERSON>", "renderSlides", "preCloneSlides", "postCloneSlides", "childOnClickOptions", "childStyle", "slideClass", "slideClasses", "outline", "preCloneNo", "reverse", "_React$PureComponent", "_len", "args", "_key", "mouseEvents", "handleRef", "PureComponent", "getDotCount", "_rightBound", "rightBound", "_leftBound", "leftBound", "dotOptions", "bind", "prevClasses", "prev<PERSON><PERSON><PERSON>", "prevArrowProps", "customProps", "_React$PureComponent2", "nextClasses", "<PERSON><PERSON><PERSON><PERSON>", "nextArrowProps"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;QCVA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;;;QAGA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA,0CAA0C,gCAAgC;QAC1E;QACA;;QAEA;QACA;QACA;QACA,wDAAwD,kBAAkB;QAC1E;QACA,iDAAiD,cAAc;QAC/D;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,yCAAyC,iCAAiC;QAC1E,gHAAgH,mBAAmB,EAAE;QACrI;QACA;;QAEA;QACA;QACA;QACA,2BAA2B,0BAA0B,EAAE;QACvD,iCAAiC,eAAe;QAChD;QACA;QACA;;QAEA;QACA,sDAAsD,+DAA+D;;QAErH;QACA;;;QAGA;QACA;;;;;;;;AClFA;AAAA;AAA8B;AAEfA,8GAAM,E;;;;;;;ACFrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAa;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAAAC,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,GAAAE,4EAAA,CAAAF,CAAA,GAAAG,uFAAA,CAAAJ,CAAA,EAAAK,sFAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAN,CAAA,EAAAC,CAAA,QAAAC,4EAAA,CAAAH,CAAA,EAAAQ,WAAA,IAAAP,CAAA,CAAAQ,KAAA,CAAAT,CAAA,EAAAE,CAAA;AAEa;AACmB;AACf;AACa;AAC0B;AAAA,IAEhDJ,MAAM,0BAAAY,gBAAA;EACzB,SAAAZ,OAAYa,KAAK,EAAE;IAAA,IAAAC,KAAA;IAAAC,4EAAA,OAAAf,MAAA;IACjBc,KAAA,GAAAb,UAAA,OAAAD,MAAA,GAAMa,KAAK;IAAEG,4EAAA,CAAAF,KAAA,2BAOS,UAACG,GAAG;MAAA,OAAMH,KAAA,CAAKI,WAAW,GAAGD,GAAG;IAAA,CAAC;IAAAD,4EAAA,CAAAF,KAAA,eAgE7C;MAAA,OAAMA,KAAA,CAAKI,WAAW,CAACC,SAAS,CAAC,CAAC;IAAA;IAAAH,4EAAA,CAAAF,KAAA,eAElC;MAAA,OAAMA,KAAA,CAAKI,WAAW,CAACE,SAAS,CAAC,CAAC;IAAA;IAAAJ,4EAAA,CAAAF,KAAA,eAElC,UAACO,KAAK;MAAA,IAAEC,WAAW,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MAAA,OACrCT,KAAA,CAAKI,WAAW,CAACQ,SAAS,CAACL,KAAK,EAAEC,WAAW,CAAC;IAAA;IAAAN,4EAAA,CAAAF,KAAA,gBAEnC;MAAA,OAAMA,KAAA,CAAKI,WAAW,CAACS,KAAK,CAAC,QAAQ,CAAC;IAAA;IAAAX,4EAAA,CAAAF,KAAA,eAEvC;MAAA,OAAMA,KAAA,CAAKI,WAAW,CAACU,QAAQ,CAAC,MAAM,CAAC;IAAA;IA/EjDd,KAAA,CAAKe,KAAK,GAAG;MACXC,UAAU,EAAE;IACd,CAAC;IACDhB,KAAA,CAAKiB,wBAAwB,GAAG,EAAE;IAAC,OAAAjB,KAAA;EACrC;EAACkB,sEAAA,CAAAhC,MAAA,EAAAY,gBAAA;EAAA,OAAAqB,yEAAA,CAAAjC,MAAA;IAAAkC,GAAA;IAAAC,KAAA,EAID,SAAAC,MAAMC,KAAK,EAAEC,OAAO,EAAE;MACpB;MACA,IAAMC,GAAG,GAAGC,MAAM,CAACC,UAAU,CAACJ,KAAK,CAAC;MACpC,IAAMK,QAAQ,GAAG,SAAXA,QAAQA,CAAAC,IAAA,EAAoB;QAAA,IAAdC,OAAO,GAAAD,IAAA,CAAPC,OAAO;QACzB,IAAIA,OAAO,EAAE;UACXN,OAAO,CAAC,CAAC;QACX;MACF,CAAC;MACDC,GAAG,CAACM,WAAW,CAACH,QAAQ,CAAC;MACzBA,QAAQ,CAACH,GAAG,CAAC;MACb,IAAI,CAACR,wBAAwB,CAACe,IAAI,CAAC;QAAEP,GAAG,EAAHA,GAAG;QAAEF,KAAK,EAALA,KAAK;QAAEK,QAAQ,EAARA;MAAS,CAAC,CAAC;IAC9D;;IAEA;EAAA;IAAAR,GAAA;IAAAC,KAAA,EACA,SAAAY,kBAAA,EAAoB;MAAA,IAAAC,MAAA;MAClB;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAACnC,KAAK,CAACoC,UAAU,EAAE;QACzB,IAAIC,WAAW,GAAG,IAAI,CAACrC,KAAK,CAACoC,UAAU,CAACE,GAAG,CACzC,UAACC,OAAO;UAAA,OAAKA,OAAO,CAACtB,UAAU;QAAA,CACjC,CAAC;QACD;QACAoB,WAAW,CAACG,IAAI,CAAC,UAACC,CAAC,EAAEC,CAAC;UAAA,OAAKD,CAAC,GAAGC,CAAC;QAAA,EAAC;QAEjCL,WAAW,CAACM,OAAO,CAAC,UAAC1B,UAAU,EAAE2B,KAAK,EAAK;UACzC;UACA,IAAIC,MAAM;UACV,IAAID,KAAK,KAAK,CAAC,EAAE;YACfC,MAAM,GAAGC,+CAAO,CAAC;cAAEC,QAAQ,EAAE,CAAC;cAAEC,QAAQ,EAAE/B;YAAW,CAAC,CAAC;UACzD,CAAC,MAAM;YACL4B,MAAM,GAAGC,+CAAO,CAAC;cACfC,QAAQ,EAAEV,WAAW,CAACO,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;cACpCI,QAAQ,EAAE/B;YACZ,CAAC,CAAC;UACJ;UACA;UACAgC,0EAAS,CAAC,CAAC,IACTd,MAAI,CAACZ,KAAK,CAACsB,MAAM,EAAE,YAAM;YACvBV,MAAI,CAACe,QAAQ,CAAC;cAAEjC,UAAU,EAAEA;YAAW,CAAC,CAAC;UAC3C,CAAC,CAAC;QACN,CAAC,CAAC;;QAEF;QACA;QACA,IAAIO,KAAK,GAAGsB,+CAAO,CAAC;UAAEC,QAAQ,EAAEV,WAAW,CAACc,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAAE,CAAC,CAAC;QAE3DF,0EAAS,CAAC,CAAC,IACT,IAAI,CAAC1B,KAAK,CAACC,KAAK,EAAE,YAAM;UACtBW,MAAI,CAACe,QAAQ,CAAC;YAAEjC,UAAU,EAAE;UAAK,CAAC,CAAC;QACrC,CAAC,CAAC;MACN;IACF;EAAC;IAAAI,GAAA;IAAAC,KAAA,EAED,SAAA8B,qBAAA,EAAuB;MACrB,IAAI,CAAClC,wBAAwB,CAACyB,OAAO,CAAC,UAAUU,GAAG,EAAE;QACnDA,GAAG,CAAC3B,GAAG,CAAC4B,cAAc,CAACD,GAAG,CAACxB,QAAQ,CAAC;MACtC,CAAC,CAAC;IACJ;EAAC;IAAAR,GAAA;IAAAC,KAAA,EAaD,SAAAiC,OAAA,EAAS;MAAA,IAAAC,MAAA;MACP,IAAIC,QAAQ;MACZ,IAAIC,QAAQ;MACZ,IAAI,IAAI,CAAC1C,KAAK,CAACC,UAAU,EAAE;QACzByC,QAAQ,GAAG,IAAI,CAAC1D,KAAK,CAACoC,UAAU,CAACuB,MAAM,CACrC,UAACC,IAAI;UAAA,OAAKA,IAAI,CAAC3C,UAAU,KAAKuC,MAAI,CAACxC,KAAK,CAACC,UAAU;QAAA,CACrD,CAAC;QACDwC,QAAQ,GACNC,QAAQ,CAAC,CAAC,CAAC,CAACD,QAAQ,KAAK,SAAS,GAC9B,SAAS,GAAAI,2EAAA,CAAAA,2EAAA,CAAAA,2EAAA,KACJC,uDAAY,GAAK,IAAI,CAAC9D,KAAK,GAAK0D,QAAQ,CAAC,CAAC,CAAC,CAACD,QAAQ,CAAE;MACnE,CAAC,MAAM;QACLA,QAAQ,GAAAI,2EAAA,CAAAA,2EAAA,KAAQC,uDAAY,GAAK,IAAI,CAAC9D,KAAK,CAAE;MAC/C;;MAEA;MACA,IAAIyD,QAAQ,CAACM,UAAU,EAAE;QACvB,IACEN,QAAQ,CAACO,cAAc,GAAG,CAAC,IAC3BC,MAAoB,KAAK,YAAY,EACrC;UACAC,OAAO,CAACC,IAAI,qEAAAC,MAAA,CAC0DX,QAAQ,CAACO,cAAc,CAC7F,CAAC;QACH;QACAP,QAAQ,CAACO,cAAc,GAAG,CAAC;MAC7B;MACA;MACA,IAAIP,QAAQ,CAACY,IAAI,EAAE;QACjB,IAAIZ,QAAQ,CAACa,YAAY,GAAG,CAAC,IAAIL,MAAoB,KAAK,YAAY,EAAE;UACtEC,OAAO,CAACC,IAAI,sEAAAC,MAAA,CAC2DX,QAAQ,CAACa,YAAY,CAC5F,CAAC;QACH;QACA,IACEb,QAAQ,CAACO,cAAc,GAAG,CAAC,IAC3BC,MAAoB,KAAK,YAAY,EACrC;UACAC,OAAO,CAACC,IAAI,wEAAAC,MAAA,CAC6DX,QAAQ,CAACO,cAAc,CAChG,CAAC;QACH;QACAP,QAAQ,CAACa,YAAY,GAAG,CAAC;QACzBb,QAAQ,CAACO,cAAc,GAAG,CAAC;MAC7B;;MAEA;MACA,IAAIO,QAAQ,GAAGC,4CAAK,CAACC,QAAQ,CAACC,OAAO,CAAC,IAAI,CAAC1E,KAAK,CAACuE,QAAQ,CAAC;;MAE1D;MACA;MACAA,QAAQ,GAAGA,QAAQ,CAACZ,MAAM,CAAC,UAACgB,KAAK,EAAK;QACpC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;UAC7B,OAAO,CAAC,CAACA,KAAK,CAACC,IAAI,CAAC,CAAC;QACvB;QACA,OAAO,CAAC,CAACD,KAAK;MAChB,CAAC,CAAC;;MAEF;MACA,IACElB,QAAQ,CAACoB,aAAa,KACrBpB,QAAQ,CAACqB,IAAI,GAAG,CAAC,IAAIrB,QAAQ,CAACsB,YAAY,GAAG,CAAC,CAAC,EAChD;QACAb,OAAO,CAACC,IAAI,yEAEZ,CAAC;QACDV,QAAQ,CAACoB,aAAa,GAAG,KAAK;MAChC;MACA,IAAIG,WAAW,GAAG,EAAE;MACpB,IAAIC,YAAY,GAAG,IAAI;MACvB,KACE,IAAIC,CAAC,GAAG,CAAC,EACTA,CAAC,GAAGX,QAAQ,CAAC5D,MAAM,EACnBuE,CAAC,IAAIzB,QAAQ,CAACqB,IAAI,GAAGrB,QAAQ,CAACsB,YAAY,EAC1C;QACA,IAAII,QAAQ,GAAG,EAAE;QACjB,KACE,IAAIC,CAAC,GAAGF,CAAC,EACTE,CAAC,GAAGF,CAAC,GAAGzB,QAAQ,CAACqB,IAAI,GAAGrB,QAAQ,CAACsB,YAAY,EAC7CK,CAAC,IAAI3B,QAAQ,CAACsB,YAAY,EAC1B;UACA,IAAIM,GAAG,GAAG,EAAE;UACZ,KAAK,IAAIC,CAAC,GAAGF,CAAC,EAAEE,CAAC,GAAGF,CAAC,GAAG3B,QAAQ,CAACsB,YAAY,EAAEO,CAAC,IAAI,CAAC,EAAE;YACrD,IAAI7B,QAAQ,CAACoB,aAAa,IAAIN,QAAQ,CAACe,CAAC,CAAC,CAACtF,KAAK,CAACuF,KAAK,EAAE;cACrDN,YAAY,GAAGV,QAAQ,CAACe,CAAC,CAAC,CAACtF,KAAK,CAACuF,KAAK,CAACC,KAAK;YAC9C;YACA,IAAIF,CAAC,IAAIf,QAAQ,CAAC5D,MAAM,EAAE;YAC1B0E,GAAG,CAACpD,IAAI,eACNuC,4CAAK,CAACiB,YAAY,CAAClB,QAAQ,CAACe,CAAC,CAAC,EAAE;cAC9BjE,GAAG,EAAE,GAAG,GAAG6D,CAAC,GAAG,EAAE,GAAGE,CAAC,GAAGE,CAAC;cACzBI,QAAQ,EAAE,CAAC,CAAC;cACZH,KAAK,EAAE;gBACLC,KAAK,KAAApB,MAAA,CAAK,GAAG,GAAGX,QAAQ,CAACsB,YAAY,MAAG;gBACxCY,OAAO,EAAE;cACX;YACF,CAAC,CACH,CAAC;UACH;UACAR,QAAQ,CAAClD,IAAI,eAACuC,4CAAA,CAAAoB,aAAA;YAAKvE,GAAG,EAAE,EAAE,GAAG6D,CAAC,GAAGE;UAAE,GAAEC,GAAS,CAAC,CAAC;QAClD;QACA,IAAI5B,QAAQ,CAACoB,aAAa,EAAE;UAC1BG,WAAW,CAAC/C,IAAI,eACduC,4CAAA,CAAAoB,aAAA;YAAKvE,GAAG,EAAE6D,CAAE;YAACK,KAAK,EAAE;cAAEC,KAAK,EAAEP;YAAa;UAAE,GACzCE,QACE,CACP,CAAC;QACH,CAAC,MAAM;UACLH,WAAW,CAAC/C,IAAI,eAACuC,4CAAA,CAAAoB,aAAA;YAAKvE,GAAG,EAAE6D;UAAE,GAAEC,QAAc,CAAC,CAAC;QACjD;MACF;MAEA,IAAI1B,QAAQ,KAAK,SAAS,EAAE;QAC1B,IAAMoC,SAAS,GAAG,iBAAiB,IAAI,IAAI,CAAC7F,KAAK,CAAC6F,SAAS,IAAI,EAAE,CAAC;QAClE,oBAAOrB,4CAAA,CAAAoB,aAAA;UAAKC,SAAS,EAAEA;QAAU,GAAEtB,QAAc,CAAC;MACpD,CAAC,MAAM,IACLS,WAAW,CAACrE,MAAM,IAAI8C,QAAQ,CAACa,YAAY,IAC3C,CAACb,QAAQ,CAACqC,QAAQ,EAClB;QACArC,QAAQ,CAACsC,OAAO,GAAG,IAAI;MACzB;MACA,oBACEvB,4CAAA,CAAAoB,aAAA,CAACI,0DAAW,EAAAC,qEAAA;QACVV,KAAK,EAAE,IAAI,CAACvF,KAAK,CAACuF,KAAM;QACxBnF,GAAG,EAAE,IAAI,CAAC8F;MAAsB,GAC5BC,+EAAc,CAAC1C,QAAQ,CAAC,GAE3BuB,WACU,CAAC;IAElB;EAAC;AAAA,EArNiCR,4CAAK,CAAC4B,SAAS;;;;;;;ACRnD;AACA;AACA,mBAAmB,sBAAsB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,wG;;;;;;ACdA,qBAAqB,mBAAO,CAAC,CAAqB;AAClD;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,iBAAiB,sBAAsB;AACvC;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA;AACA;AACA,8G;;;;;;ACtBA,oBAAoB,mBAAO,CAAC,CAAoB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA,+G;;;;;;ACfA,cAAc,mBAAO,CAAC,CAAa;AACnC,kBAAkB,mBAAO,CAAC,CAAkB;AAC5C;AACA;AACA;AACA;AACA,6G;;;;;;ACNA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA,uG;;;;;;ACTA,cAAc,mBAAO,CAAC,CAAa;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2G;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA,+G;;;;;;ACLA,oBAAoB,mBAAO,CAAC,CAAoB;AAChD;AACA,iBAAiB,kBAAkB;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,4G;;;;;;AClBA,cAAc,mBAAO,CAAC,CAAa;AACnC,4BAA4B,mBAAO,CAAC,EAA4B;AAChE;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,0H;;;;;;ACVA;AACA;AACA;AACA;AACA;AACA;AACA,sH;;;;;;ACNA;AACA;AACA,yFAAyF;AACzF,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA,yH;;;;;;ACRA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,+G;;;;;;ACNA,qBAAqB,mBAAO,CAAC,EAAqB;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,yG;;;;;;ACjBA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,+G;;;;;;ACPA,iD;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAa;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAC,SAAA;AAAA,SAAAjH,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,GAAAE,4EAAA,CAAAF,CAAA,GAAAG,uFAAA,CAAAJ,CAAA,EAAAK,sFAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAN,CAAA,EAAAC,CAAA,QAAAC,4EAAA,CAAAH,CAAA,EAAAQ,WAAA,IAAAP,CAAA,CAAAQ,KAAA,CAAAT,CAAA,EAAAE,CAAA;AAEa;AACiB;AACE;AACT;AAiBF;AAEF;AACF;AACkB;AACM;AAE/C,IAAMyG,WAAW,0BAAAjG,gBAAA;EACtB,SAAAiG,YAAYhG,KAAK,EAAE;IAAA,IAAAC,KAAA;IAAAC,4EAAA,OAAA8F,WAAA;IACjB/F,KAAA,GAAAb,UAAA,OAAA4G,WAAA,GAAMhG,KAAK;IAAEG,6EAAA,CAAAF,KAAA,oBAeE,UAACG,GAAG;MAAA,OAAMH,KAAA,CAAKqG,IAAI,GAAGlG,GAAG;IAAA,CAAC;IAAAD,6EAAA,CAAAF,KAAA,qBACzB,UAACG,GAAG;MAAA,OAAMH,KAAA,CAAKsG,KAAK,GAAGnG,GAAG;IAAA,CAAC;IAAAD,6EAAA,CAAAF,KAAA,iBAC/B,YAAM;MAClB,IAAIA,KAAA,CAAKD,KAAK,CAACwG,cAAc,IAAIvG,KAAA,CAAKqG,IAAI,EAAE;QAC1C,IAAMG,IAAI,GAAGxG,KAAA,CAAKqG,IAAI,CAACI,aAAa,kBAAAtC,MAAA,CAClBnE,KAAA,CAAKe,KAAK,CAAC2F,YAAY,QACzC,CAAC;QACD1G,KAAA,CAAKqG,IAAI,CAACf,KAAK,CAACqB,MAAM,GAAGC,0EAAS,CAACJ,IAAI,CAAC,GAAG,IAAI;MACjD;IACF,CAAC;IAAAtG,6EAAA,CAAAF,KAAA,uBACmB,YAAM;MACxBA,KAAA,CAAKD,KAAK,CAAC8G,MAAM,IAAI7G,KAAA,CAAKD,KAAK,CAAC8G,MAAM,CAAC,CAAC;MACxC,IAAI7G,KAAA,CAAKD,KAAK,CAAC+G,QAAQ,EAAE;QACvB,IAAIC,YAAY,GAAGC,sFAAqB,CAAApD,2EAAA,CAAAA,2EAAA,KACnC5D,KAAA,CAAKD,KAAK,GACVC,KAAA,CAAKe,KAAK,CACd,CAAC;QACF,IAAIgG,YAAY,CAACrG,MAAM,GAAG,CAAC,EAAE;UAC3BV,KAAA,CAAKiD,QAAQ,CAAC,UAACgE,SAAS;YAAA,OAAM;cAC5BC,cAAc,EAAED,SAAS,CAACC,cAAc,CAAC/C,MAAM,CAAC4C,YAAY;YAC9D,CAAC;UAAA,CAAC,CAAC;UACH,IAAI/G,KAAA,CAAKD,KAAK,CAACoH,UAAU,EAAE;YACzBnH,KAAA,CAAKD,KAAK,CAACoH,UAAU,CAACJ,YAAY,CAAC;UACrC;QACF;MACF;MACA,IAAIK,IAAI,GAAAxD,2EAAA;QAAKyD,OAAO,EAAErH,KAAA,CAAKqG,IAAI;QAAEiB,QAAQ,EAAEtH,KAAA,CAAKsG;MAAK,GAAKtG,KAAA,CAAKD,KAAK,CAAE;MACtEC,KAAA,CAAKuH,WAAW,CAACH,IAAI,EAAE,IAAI,EAAE,YAAM;QACjCpH,KAAA,CAAKwH,WAAW,CAAC,CAAC;QAClBxH,KAAA,CAAKD,KAAK,CAAC0H,QAAQ,IAAIzH,KAAA,CAAKc,QAAQ,CAAC,SAAS,CAAC;MACjD,CAAC,CAAC;MACF,IAAId,KAAA,CAAKD,KAAK,CAAC+G,QAAQ,KAAK,aAAa,EAAE;QACzC9G,KAAA,CAAK0H,aAAa,GAAGC,WAAW,CAAC3H,KAAA,CAAK4H,mBAAmB,EAAE,IAAI,CAAC;MAClE;MACA5H,KAAA,CAAK6H,EAAE,GAAG,IAAIC,iEAAc,CAAC,YAAM;QACjC,IAAI9H,KAAA,CAAKe,KAAK,CAACgH,SAAS,EAAE;UACxB/H,KAAA,CAAKgI,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;UAC7BhI,KAAA,CAAKiI,cAAc,CAACjG,IAAI,CACtBkG,UAAU,CAAC;YAAA,OAAMlI,KAAA,CAAKgI,eAAe,CAAC,CAAC;UAAA,GAAEhI,KAAA,CAAKD,KAAK,CAACoI,KAAK,CAC3D,CAAC;QACH,CAAC,MAAM;UACLnI,KAAA,CAAKgI,eAAe,CAAC,CAAC;QACxB;MACF,CAAC,CAAC;MACFhI,KAAA,CAAK6H,EAAE,CAACO,OAAO,CAACpI,KAAA,CAAKqG,IAAI,CAAC;MAC1BgC,QAAQ,CAACC,gBAAgB,IACvBC,KAAK,CAACC,SAAS,CAAC9F,OAAO,CAAC+F,IAAI,CAC1BJ,QAAQ,CAACC,gBAAgB,CAAC,cAAc,CAAC,EACzC,UAAC/H,KAAK,EAAK;QACTA,KAAK,CAACmI,OAAO,GAAG1I,KAAA,CAAKD,KAAK,CAAC4I,YAAY,GAAG3I,KAAA,CAAK4I,YAAY,GAAG,IAAI;QAClErI,KAAK,CAACsI,MAAM,GAAG7I,KAAA,CAAKD,KAAK,CAAC4I,YAAY,GAAG3I,KAAA,CAAK8I,WAAW,GAAG,IAAI;MAClE,CACF,CAAC;MACH,IAAIpH,MAAM,CAACqH,gBAAgB,EAAE;QAC3BrH,MAAM,CAACqH,gBAAgB,CAAC,QAAQ,EAAE/I,KAAA,CAAKgI,eAAe,CAAC;MACzD,CAAC,MAAM;QACLtG,MAAM,CAACsH,WAAW,CAAC,UAAU,EAAEhJ,KAAA,CAAKgI,eAAe,CAAC;MACtD;IACF,CAAC;IAAA9H,6EAAA,CAAAF,KAAA,0BACsB,YAAM;MAC3B,IAAIA,KAAA,CAAKiJ,oBAAoB,EAAE;QAC7BC,YAAY,CAAClJ,KAAA,CAAKiJ,oBAAoB,CAAC;MACzC;MACA,IAAIjJ,KAAA,CAAK0H,aAAa,EAAE;QACtByB,aAAa,CAACnJ,KAAA,CAAK0H,aAAa,CAAC;MACnC;MACA,IAAI1H,KAAA,CAAKiI,cAAc,CAACvH,MAAM,EAAE;QAC9BV,KAAA,CAAKiI,cAAc,CAACvF,OAAO,CAAC,UAAC0G,KAAK;UAAA,OAAKF,YAAY,CAACE,KAAK,CAAC;QAAA,EAAC;QAC3DpJ,KAAA,CAAKiI,cAAc,GAAG,EAAE;MAC1B;MACA,IAAIvG,MAAM,CAACqH,gBAAgB,EAAE;QAC3BrH,MAAM,CAAC2H,mBAAmB,CAAC,QAAQ,EAAErJ,KAAA,CAAKgI,eAAe,CAAC;MAC5D,CAAC,MAAM;QACLtG,MAAM,CAAC4H,WAAW,CAAC,UAAU,EAAEtJ,KAAA,CAAKgI,eAAe,CAAC;MACtD;MACA,IAAIhI,KAAA,CAAKuJ,aAAa,EAAE;QACtBJ,aAAa,CAACnJ,KAAA,CAAKuJ,aAAa,CAAC;MACnC;MACAvJ,KAAA,CAAK6H,EAAE,CAAC2B,UAAU,CAAC,CAAC;IACtB,CAAC;IAAAtJ,6EAAA,CAAAF,KAAA,wBA6BoB,UAACyJ,SAAS,EAAK;MAClCzJ,KAAA,CAAK0J,eAAe,CAAC,CAAC;MACtB1J,KAAA,CAAKD,KAAK,CAAC4J,QAAQ,IAAI3J,KAAA,CAAKD,KAAK,CAAC4J,QAAQ,CAAC,CAAC;MAC5C,IAAI3J,KAAA,CAAKD,KAAK,CAAC+G,QAAQ,EAAE;QACvB,IAAIC,YAAY,GAAGC,sFAAqB,CAAApD,2EAAA,CAAAA,2EAAA,KACnC5D,KAAA,CAAKD,KAAK,GACVC,KAAA,CAAKe,KAAK,CACd,CAAC;QACF,IAAIgG,YAAY,CAACrG,MAAM,GAAG,CAAC,EAAE;UAC3BV,KAAA,CAAKiD,QAAQ,CAAC,UAACgE,SAAS;YAAA,OAAM;cAC5BC,cAAc,EAAED,SAAS,CAACC,cAAc,CAAC/C,MAAM,CAAC4C,YAAY;YAC9D,CAAC;UAAA,CAAC,CAAC;UACH,IAAI/G,KAAA,CAAKD,KAAK,CAACoH,UAAU,EAAE;YACzBnH,KAAA,CAAKD,KAAK,CAACoH,UAAU,CAACJ,YAAY,CAAC;UACrC;QACF;MACF;MACA;MACA;MACA;MACA/G,KAAA,CAAKwH,WAAW,CAAC,CAAC;MAClB,IAAIJ,IAAI,GAAAxD,2EAAA,CAAAA,2EAAA;QACNyD,OAAO,EAAErH,KAAA,CAAKqG,IAAI;QAClBiB,QAAQ,EAAEtH,KAAA,CAAKsG;MAAK,GACjBtG,KAAA,CAAKD,KAAK,GACVC,KAAA,CAAKe,KAAK,CACd;MACD,IAAM6I,aAAa,GAAG5J,KAAA,CAAK6J,cAAc,CAACJ,SAAS,CAAC;MACpDG,aAAa,IACX5J,KAAA,CAAKuH,WAAW,CAACH,IAAI,EAAEwC,aAAa,EAAE,YAAM;QAC1C,IACE5J,KAAA,CAAKe,KAAK,CAAC2F,YAAY,IAAInC,6CAAK,CAACC,QAAQ,CAACsF,KAAK,CAAC9J,KAAA,CAAKD,KAAK,CAACuE,QAAQ,CAAC,EACpE;UACAtE,KAAA,CAAK+J,WAAW,CAAC;YACfC,OAAO,EAAE,OAAO;YAChBrH,KAAK,EACH4B,6CAAK,CAACC,QAAQ,CAACsF,KAAK,CAAC9J,KAAA,CAAKD,KAAK,CAACuE,QAAQ,CAAC,GACzCtE,KAAA,CAAKD,KAAK,CAACsE,YAAY;YACzBqC,YAAY,EAAE1G,KAAA,CAAKe,KAAK,CAAC2F;UAC3B,CAAC,CAAC;QACJ;QACA,IACE+C,SAAS,CAAChC,QAAQ,KAAKzH,KAAA,CAAKD,KAAK,CAAC0H,QAAQ,IAC1CgC,SAAS,CAACQ,aAAa,KAAKjK,KAAA,CAAKD,KAAK,CAACkK,aAAa,EACpD;UACA,IAAI,CAACR,SAAS,CAAChC,QAAQ,IAAIzH,KAAA,CAAKD,KAAK,CAAC0H,QAAQ,EAAE;YAC9CzH,KAAA,CAAKc,QAAQ,CAAC,SAAS,CAAC;UAC1B,CAAC,MAAM,IAAId,KAAA,CAAKD,KAAK,CAAC0H,QAAQ,EAAE;YAC9BzH,KAAA,CAAKc,QAAQ,CAAC,QAAQ,CAAC;UACzB,CAAC,MAAM;YACLd,KAAA,CAAKa,KAAK,CAAC,QAAQ,CAAC;UACtB;QACF;MACF,CAAC,CAAC;IACN,CAAC;IAAAX,6EAAA,CAAAF,KAAA,qBACiB,UAAC4J,aAAa,EAAK;MACnC,IAAI5J,KAAA,CAAKkK,eAAe,EAAElK,KAAA,CAAKkK,eAAe,CAACC,MAAM,CAAC,CAAC;MACvDnK,KAAA,CAAKkK,eAAe,GAAGE,mEAAQ,CAAC,EAAE,EAAE;QAAA,OAAMpK,KAAA,CAAKqK,YAAY,CAACT,aAAa,CAAC;MAAA,EAAC;MAC3E5J,KAAA,CAAKkK,eAAe,CAAC,CAAC;IACxB,CAAC;IAAAhK,6EAAA,CAAAF,KAAA,kBACc,YAA0B;MAAA,IAAzB4J,aAAa,GAAAnJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MAClC,IAAM6J,cAAc,GAAGC,OAAO,CAACvK,KAAA,CAAKsG,KAAK,IAAItG,KAAA,CAAKsG,KAAK,CAACkE,IAAI,CAAC;MAC7D;MACA,IAAI,CAACF,cAAc,EAAE;MACrB,IAAIlD,IAAI,GAAAxD,2EAAA,CAAAA,2EAAA;QACNyD,OAAO,EAAErH,KAAA,CAAKqG,IAAI;QAClBiB,QAAQ,EAAEtH,KAAA,CAAKsG;MAAK,GACjBtG,KAAA,CAAKD,KAAK,GACVC,KAAA,CAAKe,KAAK,CACd;MACDf,KAAA,CAAKuH,WAAW,CAACH,IAAI,EAAEwC,aAAa,EAAE,YAAM;QAC1C,IAAI5J,KAAA,CAAKD,KAAK,CAAC0H,QAAQ,EAAEzH,KAAA,CAAKc,QAAQ,CAAC,QAAQ,CAAC,CAAC,KAC5Cd,KAAA,CAAKa,KAAK,CAAC,QAAQ,CAAC;MAC3B,CAAC,CAAC;MACF;MACAb,KAAA,CAAKiD,QAAQ,CAAC;QACZ8E,SAAS,EAAE;MACb,CAAC,CAAC;MACFmB,YAAY,CAAClJ,KAAA,CAAKiJ,oBAAoB,CAAC;MACvC,OAAOjJ,KAAA,CAAKiJ,oBAAoB;IAClC,CAAC;IAAA/I,6EAAA,CAAAF,KAAA,iBACa,UAACoH,IAAI,EAAEwC,aAAa,EAAEa,QAAQ,EAAK;MAC/C,IAAIC,YAAY,GAAGC,iFAAgB,CAACvD,IAAI,CAAC;MACzCA,IAAI,GAAAxD,2EAAA,CAAAA,2EAAA,CAAAA,2EAAA,KAAQwD,IAAI,GAAKsD,YAAY;QAAEE,UAAU,EAAEF,YAAY,CAAChE;MAAY,EAAE;MAC1E,IAAImE,UAAU,GAAGC,6EAAY,CAAC1D,IAAI,CAAC;MACnCA,IAAI,GAAAxD,2EAAA,CAAAA,2EAAA,KAAQwD,IAAI;QAAE2D,IAAI,EAAEF;MAAU,EAAE;MACpC,IAAIG,UAAU,GAAGC,4EAAW,CAAC7D,IAAI,CAAC;MAClC,IACEwC,aAAa,IACbrF,6CAAK,CAACC,QAAQ,CAACsF,KAAK,CAAC9J,KAAA,CAAKD,KAAK,CAACuE,QAAQ,CAAC,KACvCC,6CAAK,CAACC,QAAQ,CAACsF,KAAK,CAAC1C,IAAI,CAAC9C,QAAQ,CAAC,EACrC;QACAoG,YAAY,CAAC,YAAY,CAAC,GAAGM,UAAU;MACzC;MACAhL,KAAA,CAAKiD,QAAQ,CAACyH,YAAY,EAAED,QAAQ,CAAC;IACvC,CAAC;IAAAvK,6EAAA,CAAAF,KAAA,aAES,YAAM;MACd,IAAIA,KAAA,CAAKD,KAAK,CAAC6E,aAAa,EAAE;QAC5B,IAAIsG,WAAU,GAAG,CAAC;UAChBC,UAAS,GAAG,CAAC;QACf,IAAIC,cAAc,GAAG,EAAE;QACvB,IAAIC,SAAS,GAAGC,6EAAY,CAAA1H,2EAAA,CAAAA,2EAAA,CAAAA,2EAAA,KACvB5D,KAAA,CAAKD,KAAK,GACVC,KAAA,CAAKe,KAAK;UACbwK,UAAU,EAAEvL,KAAA,CAAKD,KAAK,CAACuE,QAAQ,CAAC5D;QAAM,EACvC,CAAC;QACF,IAAI8K,UAAU,GAAGC,8EAAa,CAAA7H,2EAAA,CAAAA,2EAAA,CAAAA,2EAAA,KACzB5D,KAAA,CAAKD,KAAK,GACVC,KAAA,CAAKe,KAAK;UACbwK,UAAU,EAAEvL,KAAA,CAAKD,KAAK,CAACuE,QAAQ,CAAC5D;QAAM,EACvC,CAAC;QACFV,KAAA,CAAKD,KAAK,CAACuE,QAAQ,CAAC5B,OAAO,CAAC,UAACgC,KAAK,EAAK;UACrC0G,cAAc,CAACpJ,IAAI,CAAC0C,KAAK,CAAC3E,KAAK,CAACuF,KAAK,CAACC,KAAK,CAAC;UAC5C2F,WAAU,IAAIxG,KAAK,CAAC3E,KAAK,CAACuF,KAAK,CAACC,KAAK;QACvC,CAAC,CAAC;QACF,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoG,SAAS,EAAEpG,CAAC,EAAE,EAAE;UAClCkG,UAAS,IAAIC,cAAc,CAACA,cAAc,CAAC1K,MAAM,GAAG,CAAC,GAAGuE,CAAC,CAAC;UAC1DiG,WAAU,IAAIE,cAAc,CAACA,cAAc,CAAC1K,MAAM,GAAG,CAAC,GAAGuE,CAAC,CAAC;QAC7D;QACA,KAAK,IAAIA,EAAC,GAAG,CAAC,EAAEA,EAAC,GAAGuG,UAAU,EAAEvG,EAAC,EAAE,EAAE;UACnCiG,WAAU,IAAIE,cAAc,CAACnG,EAAC,CAAC;QACjC;QACA,KAAK,IAAIA,GAAC,GAAG,CAAC,EAAEA,GAAC,GAAGjF,KAAA,CAAKe,KAAK,CAAC2F,YAAY,EAAEzB,GAAC,EAAE,EAAE;UAChDkG,UAAS,IAAIC,cAAc,CAACnG,GAAC,CAAC;QAChC;QACA,IAAI+F,WAAU,GAAG;UACfzF,KAAK,EAAE2F,WAAU,GAAG,IAAI;UACxBH,IAAI,EAAE,CAACI,UAAS,GAAG;QACrB,CAAC;QACD,IAAInL,KAAA,CAAKD,KAAK,CAAC+D,UAAU,EAAE;UACzB,IAAIkB,YAAY,MAAAb,MAAA,CAAMiH,cAAc,CAACpL,KAAA,CAAKe,KAAK,CAAC2F,YAAY,CAAC,OAAI;UACjEsE,WAAU,CAACD,IAAI,WAAA5G,MAAA,CAAW6G,WAAU,CAACD,IAAI,iBAAA5G,MAAA,CAAca,YAAY,aAAU;QAC/E;QACA,OAAO;UACLgG,UAAU,EAAVA;QACF,CAAC;MACH;MACA,IAAIU,aAAa,GAAGnH,6CAAK,CAACC,QAAQ,CAACsF,KAAK,CAAC9J,KAAA,CAAKD,KAAK,CAACuE,QAAQ,CAAC;MAC7D,IAAM8C,IAAI,GAAAxD,2EAAA,CAAAA,2EAAA,CAAAA,2EAAA,KAAQ5D,KAAA,CAAKD,KAAK,GAAKC,KAAA,CAAKe,KAAK;QAAEwK,UAAU,EAAEG;MAAa,EAAE;MACxE,IAAIH,UAAU,GAAGD,6EAAY,CAAClE,IAAI,CAAC,GAAGqE,8EAAa,CAACrE,IAAI,CAAC,GAAGsE,aAAa;MACzE,IAAIR,UAAU,GAAI,GAAG,GAAGlL,KAAA,CAAKD,KAAK,CAACsE,YAAY,GAAIkH,UAAU;MAC7D,IAAII,UAAU,GAAG,GAAG,GAAGJ,UAAU;MACjC,IAAIJ,SAAS,GACV,CAACQ,UAAU,IACTL,6EAAY,CAAClE,IAAI,CAAC,GAAGpH,KAAA,CAAKe,KAAK,CAAC2F,YAAY,CAAC,GAC9CwE,UAAU,GACZ,GAAG;MACL,IAAIlL,KAAA,CAAKD,KAAK,CAAC+D,UAAU,EAAE;QACzBqH,SAAS,IAAI,CAAC,GAAG,GAAIQ,UAAU,GAAGT,UAAU,GAAI,GAAG,IAAI,CAAC;MAC1D;MACA,IAAIF,UAAU,GAAG;QACfzF,KAAK,EAAE2F,UAAU,GAAG,GAAG;QACvBH,IAAI,EAAEI,SAAS,GAAG;MACpB,CAAC;MACD,OAAO;QACLQ,UAAU,EAAEA,UAAU,GAAG,GAAG;QAC5BX,UAAU,EAAEA;MACd,CAAC;IACH,CAAC;IAAA9K,6EAAA,CAAAF,KAAA,qBACiB,YAAM;MACtB,IAAI4L,MAAM,GACP5L,KAAA,CAAKqG,IAAI,IACRrG,KAAA,CAAKqG,IAAI,CAACiC,gBAAgB,IAC1BtI,KAAA,CAAKqG,IAAI,CAACiC,gBAAgB,CAAC,kBAAkB,CAAC,IAChD,EAAE;MACJ,IAAIuD,WAAW,GAAGD,MAAM,CAAClL,MAAM;QAC7BoL,WAAW,GAAG,CAAC;MACjBvD,KAAK,CAACC,SAAS,CAAC9F,OAAO,CAAC+F,IAAI,CAACmD,MAAM,EAAE,UAACG,KAAK,EAAK;QAC9C,IAAMvK,OAAO,GAAG,SAAVA,OAAOA,CAAA;UAAA,OACX,EAAEsK,WAAW,IAAIA,WAAW,IAAID,WAAW,IAAI7L,KAAA,CAAKgI,eAAe,CAAC,CAAC;QAAA;QACvE,IAAI,CAAC+D,KAAK,CAACC,OAAO,EAAE;UAClBD,KAAK,CAACC,OAAO,GAAG;YAAA,OAAMD,KAAK,CAACE,UAAU,CAACC,KAAK,CAAC,CAAC;UAAA;QAChD,CAAC,MAAM;UACL,IAAMC,gBAAgB,GAAGJ,KAAK,CAACC,OAAO;UACtCD,KAAK,CAACC,OAAO,GAAG,UAAC1M,CAAC,EAAK;YACrB6M,gBAAgB,CAAC7M,CAAC,CAAC;YACnByM,KAAK,CAACE,UAAU,CAACC,KAAK,CAAC,CAAC;UAC1B,CAAC;QACH;QACA,IAAI,CAACH,KAAK,CAACK,MAAM,EAAE;UACjB,IAAIpM,KAAA,CAAKD,KAAK,CAAC+G,QAAQ,EAAE;YACvBiF,KAAK,CAACK,MAAM,GAAG,YAAM;cACnBpM,KAAA,CAAKwH,WAAW,CAAC,CAAC;cAClBxH,KAAA,CAAKiI,cAAc,CAACjG,IAAI,CACtBkG,UAAU,CAAClI,KAAA,CAAKgI,eAAe,EAAEhI,KAAA,CAAKD,KAAK,CAACoI,KAAK,CACnD,CAAC;YACH,CAAC;UACH,CAAC,MAAM;YACL4D,KAAK,CAACK,MAAM,GAAG5K,OAAO;YACtBuK,KAAK,CAACM,OAAO,GAAG,YAAM;cACpB7K,OAAO,CAAC,CAAC;cACTxB,KAAA,CAAKD,KAAK,CAACuM,eAAe,IAAItM,KAAA,CAAKD,KAAK,CAACuM,eAAe,CAAC,CAAC;YAC5D,CAAC;UACH;QACF;MACF,CAAC,CAAC;IACJ,CAAC;IAAApM,6EAAA,CAAAF,KAAA,yBACqB,YAAM;MAC1B,IAAI+G,YAAY,GAAG,EAAE;MACrB,IAAMK,IAAI,GAAAxD,2EAAA,CAAAA,2EAAA,KAAQ5D,KAAA,CAAKD,KAAK,GAAKC,KAAA,CAAKe,KAAK,CAAE;MAC7C,KACE,IAAI4B,KAAK,GAAG3C,KAAA,CAAKe,KAAK,CAAC2F,YAAY,EACnC/D,KAAK,GAAG3C,KAAA,CAAKe,KAAK,CAACwK,UAAU,GAAGE,8EAAa,CAACrE,IAAI,CAAC,EACnDzE,KAAK,EAAE,EACP;QACA,IAAI3C,KAAA,CAAKe,KAAK,CAACmG,cAAc,CAACqF,OAAO,CAAC5J,KAAK,CAAC,GAAG,CAAC,EAAE;UAChDoE,YAAY,CAAC/E,IAAI,CAACW,KAAK,CAAC;UACxB;QACF;MACF;MACA,KACE,IAAIA,MAAK,GAAG3C,KAAA,CAAKe,KAAK,CAAC2F,YAAY,GAAG,CAAC,EACvC/D,MAAK,IAAI,CAAC2I,6EAAY,CAAClE,IAAI,CAAC,EAC5BzE,MAAK,EAAE,EACP;QACA,IAAI3C,KAAA,CAAKe,KAAK,CAACmG,cAAc,CAACqF,OAAO,CAAC5J,MAAK,CAAC,GAAG,CAAC,EAAE;UAChDoE,YAAY,CAAC/E,IAAI,CAACW,MAAK,CAAC;UACxB;QACF;MACF;MACA,IAAIoE,YAAY,CAACrG,MAAM,GAAG,CAAC,EAAE;QAC3BV,KAAA,CAAKiD,QAAQ,CAAC,UAAClC,KAAK;UAAA,OAAM;YACxBmG,cAAc,EAAEnG,KAAK,CAACmG,cAAc,CAAC/C,MAAM,CAAC4C,YAAY;UAC1D,CAAC;QAAA,CAAC,CAAC;QACH,IAAI/G,KAAA,CAAKD,KAAK,CAACoH,UAAU,EAAE;UACzBnH,KAAA,CAAKD,KAAK,CAACoH,UAAU,CAACJ,YAAY,CAAC;QACrC;MACF,CAAC,MAAM;QACL,IAAI/G,KAAA,CAAK0H,aAAa,EAAE;UACtByB,aAAa,CAACnJ,KAAA,CAAK0H,aAAa,CAAC;UACjC,OAAO1H,KAAA,CAAK0H,aAAa;QAC3B;MACF;IACF,CAAC;IAAAxH,6EAAA,CAAAF,KAAA,kBACc,UAAC2C,KAAK,EAA0B;MAAA,IAAxBnC,WAAW,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MACxC,IAAA+L,WAAA,GACExM,KAAA,CAAKD,KAAK;QADJ0M,QAAQ,GAAAD,WAAA,CAARC,QAAQ;QAAEC,YAAY,GAAAF,WAAA,CAAZE,YAAY;QAAEvF,UAAU,GAAAqF,WAAA,CAAVrF,UAAU;QAAEgB,KAAK,GAAAqE,WAAA,CAALrE,KAAK;QAAEwE,WAAW,GAAAH,WAAA,CAAXG,WAAW;MAE9D;MACA,IAAMjG,YAAY,GAAG1G,KAAA,CAAKe,KAAK,CAAC2F,YAAY;MAC5C,IAAAkG,aAAA,GAA2BC,6EAAY,CAAAjJ,2EAAA,CAAAA,2EAAA,CAAAA,2EAAA;UACrCjB,KAAK,EAALA;QAAK,GACF3C,KAAA,CAAKD,KAAK,GACVC,KAAA,CAAKe,KAAK;UACbuG,QAAQ,EAAEtH,KAAA,CAAKsG,KAAK;UACpBwG,MAAM,EAAE9M,KAAA,CAAKD,KAAK,CAAC+M,MAAM,IAAI,CAACtM;QAAW,EAC1C,CAAC;QANIO,KAAK,GAAA6L,aAAA,CAAL7L,KAAK;QAAEgM,SAAS,GAAAH,aAAA,CAATG,SAAS;MAOtB,IAAI,CAAChM,KAAK,EAAE;MACZ2L,YAAY,IAAIA,YAAY,CAAChG,YAAY,EAAE3F,KAAK,CAAC2F,YAAY,CAAC;MAC9D,IAAIK,YAAY,GAAGhG,KAAK,CAACmG,cAAc,CAACxD,MAAM,CAC5C,UAACrC,KAAK;QAAA,OAAKrB,KAAA,CAAKe,KAAK,CAACmG,cAAc,CAACqF,OAAO,CAAClL,KAAK,CAAC,GAAG,CAAC;MAAA,CACzD,CAAC;MACD8F,UAAU,IAAIJ,YAAY,CAACrG,MAAM,GAAG,CAAC,IAAIyG,UAAU,CAACJ,YAAY,CAAC;MACjE,IAAI,CAAC/G,KAAA,CAAKD,KAAK,CAACiN,cAAc,IAAIhN,KAAA,CAAKiJ,oBAAoB,EAAE;QAC3DC,YAAY,CAAClJ,KAAA,CAAKiJ,oBAAoB,CAAC;QACvC0D,WAAW,IAAIA,WAAW,CAACjG,YAAY,CAAC;QACxC,OAAO1G,KAAA,CAAKiJ,oBAAoB;MAClC;MACAjJ,KAAA,CAAKiD,QAAQ,CAAClC,KAAK,EAAE,YAAM;QACzB;QACA,IAAI0L,QAAQ,IAAIzM,KAAA,CAAKiN,aAAa,KAAKtK,KAAK,EAAE;UAC5C3C,KAAA,CAAKiN,aAAa,GAAGtK,KAAK;UAC1B8J,QAAQ,CAACrM,WAAW,CAACyM,YAAY,CAAClK,KAAK,CAAC;QAC1C;QACA,IAAI,CAACoK,SAAS,EAAE;QAChB/M,KAAA,CAAKiJ,oBAAoB,GAAGf,UAAU,CAAC,YAAM;UAC3C,IAAQH,SAAS,GAAoBgF,SAAS,CAAtChF,SAAS;YAAKmF,UAAU,GAAAC,qFAAA,CAAKJ,SAAS,EAAA3G,SAAA;UAC9CpG,KAAA,CAAKiD,QAAQ,CAACiK,UAAU,EAAE,YAAM;YAC9BlN,KAAA,CAAKiI,cAAc,CAACjG,IAAI,CACtBkG,UAAU,CAAC;cAAA,OAAMlI,KAAA,CAAKiD,QAAQ,CAAC;gBAAE8E,SAAS,EAATA;cAAU,CAAC,CAAC;YAAA,GAAE,EAAE,CACnD,CAAC;YACD4E,WAAW,IAAIA,WAAW,CAAC5L,KAAK,CAAC2F,YAAY,CAAC;YAC9C,OAAO1G,KAAA,CAAKiJ,oBAAoB;UAClC,CAAC,CAAC;QACJ,CAAC,EAAEd,KAAK,CAAC;MACX,CAAC,CAAC;IACJ,CAAC;IAAAjI,6EAAA,CAAAF,KAAA,iBACa,UAACoN,OAAO,EAA0B;MAAA,IAAxB5M,WAAW,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MACzC,IAAM2G,IAAI,GAAAxD,2EAAA,CAAAA,2EAAA,KAAQ5D,KAAA,CAAKD,KAAK,GAAKC,KAAA,CAAKe,KAAK,CAAE;MAC7C,IAAIsM,WAAW,GAAGtD,4EAAW,CAAC3C,IAAI,EAAEgG,OAAO,CAAC;MAC5C,IAAIC,WAAW,KAAK,CAAC,IAAI,CAACA,WAAW,EAAE;MACvC,IAAI7M,WAAW,KAAK,IAAI,EAAE;QACxBR,KAAA,CAAK6M,YAAY,CAACQ,WAAW,EAAE7M,WAAW,CAAC;MAC7C,CAAC,MAAM;QACLR,KAAA,CAAK6M,YAAY,CAACQ,WAAW,CAAC;MAChC;MACArN,KAAA,CAAKD,KAAK,CAAC0H,QAAQ,IAAIzH,KAAA,CAAKc,QAAQ,CAAC,QAAQ,CAAC;MAC9C,IAAId,KAAA,CAAKD,KAAK,CAACuN,aAAa,EAAE;QAC5B,IAAMC,KAAK,GAAGvN,KAAA,CAAKqG,IAAI,CAACiC,gBAAgB,CAAC,gBAAgB,CAAC;QAC1DiF,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACrB,KAAK,CAAC,CAAC;MAC9B;IACF,CAAC;IAAAhM,6EAAA,CAAAF,KAAA,kBACc,UAACV,CAAC,EAAK;MACpB,IAAIU,KAAA,CAAKwN,SAAS,KAAK,KAAK,EAAE;QAC5BlO,CAAC,CAACmO,eAAe,CAAC,CAAC;QACnBnO,CAAC,CAACoO,cAAc,CAAC,CAAC;MACpB;MACA1N,KAAA,CAAKwN,SAAS,GAAG,IAAI;IACvB,CAAC;IAAAtN,6EAAA,CAAAF,KAAA,gBACY,UAACV,CAAC,EAAK;MAClB,IAAIqO,GAAG,GAAGC,2EAAU,CAACtO,CAAC,EAAEU,KAAA,CAAKD,KAAK,CAAC8N,aAAa,EAAE7N,KAAA,CAAKD,KAAK,CAAC+N,GAAG,CAAC;MACjEH,GAAG,KAAK,EAAE,IAAI3N,KAAA,CAAK+J,WAAW,CAAC;QAAEC,OAAO,EAAE2D;MAAI,CAAC,CAAC;IAClD,CAAC;IAAAzN,6EAAA,CAAAF,KAAA,mBACe,UAACoN,OAAO,EAAK;MAC3BpN,KAAA,CAAK+J,WAAW,CAACqD,OAAO,CAAC;IAC3B,CAAC;IAAAlN,6EAAA,CAAAF,KAAA,uBACmB,YAAM;MACxB,IAAM0N,cAAc,GAAG,SAAjBA,cAAcA,CAAIpO,CAAC,EAAK;QAC5BA,CAAC,GAAGA,CAAC,IAAIoC,MAAM,CAACqM,KAAK;QACrB,IAAIzO,CAAC,CAACoO,cAAc,EAAEpO,CAAC,CAACoO,cAAc,CAAC,CAAC;QACxCpO,CAAC,CAAC0O,WAAW,GAAG,KAAK;MACvB,CAAC;MACDtM,MAAM,CAACuM,WAAW,GAAGP,cAAc;IACrC,CAAC;IAAAxN,6EAAA,CAAAF,KAAA,sBACkB,YAAM;MACvB0B,MAAM,CAACuM,WAAW,GAAG,IAAI;IAC3B,CAAC;IAAA/N,6EAAA,CAAAF,KAAA,gBACY,UAACV,CAAC,EAAK;MAClB,IAAIU,KAAA,CAAKD,KAAK,CAACmO,eAAe,EAAE;QAC9BlO,KAAA,CAAKmO,iBAAiB,CAAC,CAAC;MAC1B;MACA,IAAIpN,KAAK,GAAGqN,2EAAU,CAAC9O,CAAC,EAAEU,KAAA,CAAKD,KAAK,CAACsO,KAAK,EAAErO,KAAA,CAAKD,KAAK,CAACuO,SAAS,CAAC;MACjEvN,KAAK,KAAK,EAAE,IAAIf,KAAA,CAAKiD,QAAQ,CAAClC,KAAK,CAAC;IACtC,CAAC;IAAAb,6EAAA,CAAAF,KAAA,eACW,UAACV,CAAC,EAAK;MACjB,IAAIyB,KAAK,GAAGwN,0EAAS,CAACjP,CAAC,EAAAsE,2EAAA,CAAAA,2EAAA,CAAAA,2EAAA,KAClB5D,KAAA,CAAKD,KAAK,GACVC,KAAA,CAAKe,KAAK;QACbuG,QAAQ,EAAEtH,KAAA,CAAKsG,KAAK;QACpBe,OAAO,EAAErH,KAAA,CAAKqG,IAAI;QAClBuE,UAAU,EAAE5K,KAAA,CAAKe,KAAK,CAAC2F;MAAY,EACpC,CAAC;MACF,IAAI,CAAC3F,KAAK,EAAE;MACZ,IAAIA,KAAK,CAAC,SAAS,CAAC,EAAE;QACpBf,KAAA,CAAKwN,SAAS,GAAG,KAAK;MACxB;MACAxN,KAAA,CAAKiD,QAAQ,CAAClC,KAAK,CAAC;IACtB,CAAC;IAAAb,6EAAA,CAAAF,KAAA,cACU,UAACV,CAAC,EAAK;MAChB,IAAIyB,KAAK,GAAGyN,yEAAQ,CAAClP,CAAC,EAAAsE,2EAAA,CAAAA,2EAAA,CAAAA,2EAAA,KACjB5D,KAAA,CAAKD,KAAK,GACVC,KAAA,CAAKe,KAAK;QACbuG,QAAQ,EAAEtH,KAAA,CAAKsG,KAAK;QACpBe,OAAO,EAAErH,KAAA,CAAKqG,IAAI;QAClBuE,UAAU,EAAE5K,KAAA,CAAKe,KAAK,CAAC2F;MAAY,EACpC,CAAC;MACF,IAAI,CAAC3F,KAAK,EAAE;MACZ,IAAI0N,mBAAmB,GAAG1N,KAAK,CAAC,qBAAqB,CAAC;MACtD,OAAOA,KAAK,CAAC,qBAAqB,CAAC;MACnCf,KAAA,CAAKiD,QAAQ,CAAClC,KAAK,CAAC;MACpB,IAAI0N,mBAAmB,KAAK9N,SAAS,EAAE;MACvCX,KAAA,CAAK6M,YAAY,CAAC4B,mBAAmB,CAAC;MACtC,IAAIzO,KAAA,CAAKD,KAAK,CAACmO,eAAe,EAAE;QAC9BlO,KAAA,CAAK0O,gBAAgB,CAAC,CAAC;MACzB;IACF,CAAC;IAAAxO,6EAAA,CAAAF,KAAA,cACU,UAACV,CAAC,EAAK;MAChBU,KAAA,CAAKwO,QAAQ,CAAClP,CAAC,CAAC;MAChBU,KAAA,CAAKwN,SAAS,GAAG,IAAI;IACvB,CAAC;IAAAtN,6EAAA,CAAAF,KAAA,eACW,YAAM;MAChB;MACA;MACA;MACAA,KAAA,CAAKiI,cAAc,CAACjG,IAAI,CACtBkG,UAAU,CAAC;QAAA,OAAMlI,KAAA,CAAK+J,WAAW,CAAC;UAAEC,OAAO,EAAE;QAAW,CAAC,CAAC;MAAA,GAAE,CAAC,CAC/D,CAAC;IACH,CAAC;IAAA9J,6EAAA,CAAAF,KAAA,eACW,YAAM;MAChBA,KAAA,CAAKiI,cAAc,CAACjG,IAAI,CACtBkG,UAAU,CAAC;QAAA,OAAMlI,KAAA,CAAK+J,WAAW,CAAC;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;MAAA,GAAE,CAAC,CAC3D,CAAC;IACH,CAAC;IAAA9J,6EAAA,CAAAF,KAAA,eACW,UAACO,KAAK,EAA0B;MAAA,IAAxBC,WAAW,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MACrCF,KAAK,GAAGoO,MAAM,CAACpO,KAAK,CAAC;MACrB,IAAIqO,KAAK,CAACrO,KAAK,CAAC,EAAE,OAAO,EAAE;MAC3BP,KAAA,CAAKiI,cAAc,CAACjG,IAAI,CACtBkG,UAAU,CACR;QAAA,OACElI,KAAA,CAAK+J,WAAW,CACd;UACEC,OAAO,EAAE,OAAO;UAChBrH,KAAK,EAAEpC,KAAK;UACZmG,YAAY,EAAE1G,KAAA,CAAKe,KAAK,CAAC2F;QAC3B,CAAC,EACDlG,WACF,CAAC;MAAA,GACH,CACF,CACF,CAAC;IACH,CAAC;IAAAN,6EAAA,CAAAF,KAAA,UACM,YAAM;MACX,IAAI6O,SAAS;MACb,IAAI7O,KAAA,CAAKD,KAAK,CAAC+N,GAAG,EAAE;QAClBe,SAAS,GAAG7O,KAAA,CAAKe,KAAK,CAAC2F,YAAY,GAAG1G,KAAA,CAAKD,KAAK,CAACgE,cAAc;MACjE,CAAC,MAAM;QACL,IAAI+K,0EAAS,CAAAlL,2EAAA,CAAAA,2EAAA,KAAM5D,KAAA,CAAKD,KAAK,GAAKC,KAAA,CAAKe,KAAK,CAAE,CAAC,EAAE;UAC/C8N,SAAS,GAAG7O,KAAA,CAAKe,KAAK,CAAC2F,YAAY,GAAG1G,KAAA,CAAKD,KAAK,CAACgE,cAAc;QACjE,CAAC,MAAM;UACL,OAAO,KAAK;QACd;MACF;MAEA/D,KAAA,CAAK6M,YAAY,CAACgC,SAAS,CAAC;IAC9B,CAAC;IAAA3O,6EAAA,CAAAF,KAAA,cAEU,UAAC+O,QAAQ,EAAK;MACvB,IAAI/O,KAAA,CAAKuJ,aAAa,EAAE;QACtBJ,aAAa,CAACnJ,KAAA,CAAKuJ,aAAa,CAAC;MACnC;MACA,IAAMyF,WAAW,GAAGhP,KAAA,CAAKe,KAAK,CAACiO,WAAW;MAC1C,IAAID,QAAQ,KAAK,QAAQ,EAAE;QACzB,IACEC,WAAW,KAAK,SAAS,IACzBA,WAAW,KAAK,SAAS,IACzBA,WAAW,KAAK,QAAQ,EACxB;UACA;QACF;MACF,CAAC,MAAM,IAAID,QAAQ,KAAK,OAAO,EAAE;QAC/B,IAAIC,WAAW,KAAK,QAAQ,IAAIA,WAAW,KAAK,SAAS,EAAE;UACzD;QACF;MACF,CAAC,MAAM,IAAID,QAAQ,KAAK,MAAM,EAAE;QAC9B,IAAIC,WAAW,KAAK,QAAQ,IAAIA,WAAW,KAAK,SAAS,EAAE;UACzD;QACF;MACF;MACAhP,KAAA,CAAKuJ,aAAa,GAAG5B,WAAW,CAAC3H,KAAA,CAAKiP,IAAI,EAAEjP,KAAA,CAAKD,KAAK,CAACkK,aAAa,GAAG,EAAE,CAAC;MAC1EjK,KAAA,CAAKiD,QAAQ,CAAC;QAAE+L,WAAW,EAAE;MAAU,CAAC,CAAC;IAC3C,CAAC;IAAA9O,6EAAA,CAAAF,KAAA,WACO,UAACkP,SAAS,EAAK;MACrB,IAAIlP,KAAA,CAAKuJ,aAAa,EAAE;QACtBJ,aAAa,CAACnJ,KAAA,CAAKuJ,aAAa,CAAC;QACjCvJ,KAAA,CAAKuJ,aAAa,GAAG,IAAI;MAC3B;MACA,IAAMyF,WAAW,GAAGhP,KAAA,CAAKe,KAAK,CAACiO,WAAW;MAC1C,IAAIE,SAAS,KAAK,QAAQ,EAAE;QAC1BlP,KAAA,CAAKiD,QAAQ,CAAC;UAAE+L,WAAW,EAAE;QAAS,CAAC,CAAC;MAC1C,CAAC,MAAM,IAAIE,SAAS,KAAK,SAAS,EAAE;QAClC,IAAIF,WAAW,KAAK,SAAS,IAAIA,WAAW,KAAK,SAAS,EAAE;UAC1DhP,KAAA,CAAKiD,QAAQ,CAAC;YAAE+L,WAAW,EAAE;UAAU,CAAC,CAAC;QAC3C;MACF,CAAC,MAAM;QACL;QACA,IAAIA,WAAW,KAAK,SAAS,EAAE;UAC7BhP,KAAA,CAAKiD,QAAQ,CAAC;YAAE+L,WAAW,EAAE;UAAU,CAAC,CAAC;QAC3C;MACF;IACF,CAAC;IAAA9O,6EAAA,CAAAF,KAAA,gBACY;MAAA,OAAMA,KAAA,CAAKD,KAAK,CAAC0H,QAAQ,IAAIzH,KAAA,CAAKa,KAAK,CAAC,SAAS,CAAC;IAAA;IAAAX,6EAAA,CAAAF,KAAA,iBACjD;MAAA,OACZA,KAAA,CAAKD,KAAK,CAAC0H,QAAQ,IACnBzH,KAAA,CAAKe,KAAK,CAACiO,WAAW,KAAK,SAAS,IACpChP,KAAA,CAAKc,QAAQ,CAAC,OAAO,CAAC;IAAA;IAAAZ,6EAAA,CAAAF,KAAA,iBACV;MAAA,OAAMA,KAAA,CAAKD,KAAK,CAAC0H,QAAQ,IAAIzH,KAAA,CAAKa,KAAK,CAAC,SAAS,CAAC;IAAA;IAAAX,6EAAA,CAAAF,KAAA,kBACjD;MAAA,OACbA,KAAA,CAAKD,KAAK,CAAC0H,QAAQ,IACnBzH,KAAA,CAAKe,KAAK,CAACiO,WAAW,KAAK,SAAS,IACpChP,KAAA,CAAKc,QAAQ,CAAC,OAAO,CAAC;IAAA;IAAAZ,6EAAA,CAAAF,KAAA,kBACT;MAAA,OAAMA,KAAA,CAAKD,KAAK,CAAC0H,QAAQ,IAAIzH,KAAA,CAAKa,KAAK,CAAC,SAAS,CAAC;IAAA;IAAAX,6EAAA,CAAAF,KAAA,iBACnD;MAAA,OACZA,KAAA,CAAKD,KAAK,CAAC0H,QAAQ,IACnBzH,KAAA,CAAKe,KAAK,CAACiO,WAAW,KAAK,SAAS,IACpChP,KAAA,CAAKc,QAAQ,CAAC,MAAM,CAAC;IAAA;IAAAZ,6EAAA,CAAAF,KAAA,YAEd,YAAM;MACb,IAAI4F,SAAS,GAAGuJ,kDAAU,CAAC,cAAc,EAAEnP,KAAA,CAAKD,KAAK,CAAC6F,SAAS,EAAE;QAC/D,gBAAgB,EAAE5F,KAAA,CAAKD,KAAK,CAACqP,QAAQ;QACrC,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACF,IAAIhI,IAAI,GAAAxD,2EAAA,CAAAA,2EAAA,KAAQ5D,KAAA,CAAKD,KAAK,GAAKC,KAAA,CAAKe,KAAK,CAAE;MAC3C,IAAIsO,UAAU,GAAGC,8EAAa,CAAClI,IAAI,EAAE,CACnC,MAAM,EACN,SAAS,EACT,OAAO,EACP,UAAU,EACV,YAAY,EACZ,eAAe,EACf,cAAc,EACd,UAAU,EACV,gBAAgB,EAChB,KAAK,EACL,YAAY,EACZ,aAAa,EACb,YAAY,EACZ,UAAU,EACV,cAAc,EACd,gBAAgB,EAChB,YAAY,EACZ,YAAY,EACZ,eAAe,EACf,SAAS,EACT,eAAe,EACf,aAAa,EACb,QAAQ,CACT,CAAC;MACF,IAAQmI,YAAY,GAAKvP,KAAA,CAAKD,KAAK,CAA3BwP,YAAY;MACpBF,UAAU,GAAAzL,2EAAA,CAAAA,2EAAA,KACLyL,UAAU;QACbG,YAAY,EAAED,YAAY,GAAGvP,KAAA,CAAKyP,WAAW,GAAG,IAAI;QACpDC,YAAY,EAAEH,YAAY,GAAGvP,KAAA,CAAK2P,YAAY,GAAG,IAAI;QACrDC,WAAW,EAAEL,YAAY,GAAGvP,KAAA,CAAKyP,WAAW,GAAG,IAAI;QACnDnC,aAAa,EACXtN,KAAA,CAAKD,KAAK,CAACuN,aAAa,IAAItN,KAAA,CAAKwN,SAAS,GAAGxN,KAAA,CAAK6P,aAAa,GAAG;MAAI,EACzE;MAED,IAAIC,IAAI;MACR,IACE9P,KAAA,CAAKD,KAAK,CAAC+P,IAAI,KAAK,IAAI,IACxB9P,KAAA,CAAKe,KAAK,CAACwK,UAAU,IAAIvL,KAAA,CAAKD,KAAK,CAACsE,YAAY,EAChD;QACA,IAAI0L,QAAQ,GAAGT,8EAAa,CAAClI,IAAI,EAAE,CACjC,WAAW,EACX,YAAY,EACZ,cAAc,EACd,cAAc,EACd,gBAAgB,EAChB,cAAc,EACd,UAAU,EACV,cAAc,EACd,UAAU,EACV,YAAY,CACb,CAAC;QACF,IAAQ4I,gBAAgB,GAAKhQ,KAAA,CAAKD,KAAK,CAA/BiQ,gBAAgB;QACxBD,QAAQ,GAAAnM,2EAAA,CAAAA,2EAAA,KACHmM,QAAQ;UACXE,YAAY,EAAEjQ,KAAA,CAAK+J,WAAW;UAC9ByF,YAAY,EAAEQ,gBAAgB,GAAGhQ,KAAA,CAAKkQ,WAAW,GAAG,IAAI;UACxDN,WAAW,EAAEI,gBAAgB,GAAGhQ,KAAA,CAAKmQ,UAAU,GAAG,IAAI;UACtDT,YAAY,EAAEM,gBAAgB,GAAGhQ,KAAA,CAAKkQ,WAAW,GAAG;QAAI,EACzD;QACDJ,IAAI,gBAAGvL,6CAAA,CAAAoB,aAAA,CAACyK,2CAAI,EAAKL,QAAW,CAAC;MAC/B;MAEA,IAAIM,SAAS,EAAEC,SAAS;MACxB,IAAIC,UAAU,GAAGjB,8EAAa,CAAClI,IAAI,EAAE,CACnC,UAAU,EACV,YAAY,EACZ,cAAc,EACd,YAAY,EACZ,cAAc,EACd,WAAW,EACX,WAAW,CACZ,CAAC;MACFmJ,UAAU,CAACN,YAAY,GAAGjQ,KAAA,CAAK+J,WAAW;MAE1C,IAAI/J,KAAA,CAAKD,KAAK,CAACyQ,MAAM,EAAE;QACrBH,SAAS,gBAAG9L,6CAAA,CAAAoB,aAAA,CAAC8K,kDAAS,EAAKF,UAAa,CAAC;QACzCD,SAAS,gBAAG/L,6CAAA,CAAAoB,aAAA,CAAC+K,kDAAS,EAAKH,UAAa,CAAC;MAC3C;MAEA,IAAII,mBAAmB,GAAG,IAAI;MAE9B,IAAI3Q,KAAA,CAAKD,KAAK,CAACqP,QAAQ,EAAE;QACvBuB,mBAAmB,GAAG;UACpBhK,MAAM,EAAE3G,KAAA,CAAKe,KAAK,CAAC6P;QACrB,CAAC;MACH;MAEA,IAAIC,kBAAkB,GAAG,IAAI;MAE7B,IAAI7Q,KAAA,CAAKD,KAAK,CAACqP,QAAQ,KAAK,KAAK,EAAE;QACjC,IAAIpP,KAAA,CAAKD,KAAK,CAAC+D,UAAU,KAAK,IAAI,EAAE;UAClC+M,kBAAkB,GAAG;YACnBC,OAAO,EAAE,MAAM,GAAG9Q,KAAA,CAAKD,KAAK,CAACgR;UAC/B,CAAC;QACH;MACF,CAAC,MAAM;QACL,IAAI/Q,KAAA,CAAKD,KAAK,CAAC+D,UAAU,KAAK,IAAI,EAAE;UAClC+M,kBAAkB,GAAG;YACnBC,OAAO,EAAE9Q,KAAA,CAAKD,KAAK,CAACgR,aAAa,GAAG;UACtC,CAAC;QACH;MACF;MAEA,IAAMC,SAAS,GAAApN,2EAAA,CAAAA,2EAAA,KAAQ+M,mBAAmB,GAAKE,kBAAkB,CAAE;MACnE,IAAMI,SAAS,GAAGjR,KAAA,CAAKD,KAAK,CAACkR,SAAS;MACtC,IAAIC,SAAS,GAAG;QACdtL,SAAS,EAAE,YAAY;QACvBN,KAAK,EAAE0L,SAAS;QAChBG,OAAO,EAAEnR,KAAA,CAAKiQ,YAAY;QAC1BmB,WAAW,EAAEH,SAAS,GAAGjR,KAAA,CAAKoO,UAAU,GAAG,IAAI;QAC/CiD,WAAW,EAAErR,KAAA,CAAKe,KAAK,CAACuQ,QAAQ,IAAIL,SAAS,GAAGjR,KAAA,CAAKuO,SAAS,GAAG,IAAI;QACrEgD,SAAS,EAAEN,SAAS,GAAGjR,KAAA,CAAKwO,QAAQ,GAAG,IAAI;QAC3CkB,YAAY,EAAE1P,KAAA,CAAKe,KAAK,CAACuQ,QAAQ,IAAIL,SAAS,GAAGjR,KAAA,CAAKwO,QAAQ,GAAG,IAAI;QACrEgD,YAAY,EAAEP,SAAS,GAAGjR,KAAA,CAAKoO,UAAU,GAAG,IAAI;QAChDqD,WAAW,EAAEzR,KAAA,CAAKe,KAAK,CAACuQ,QAAQ,IAAIL,SAAS,GAAGjR,KAAA,CAAKuO,SAAS,GAAG,IAAI;QACrEmD,UAAU,EAAET,SAAS,GAAGjR,KAAA,CAAK2R,QAAQ,GAAG,IAAI;QAC5CC,aAAa,EAAE5R,KAAA,CAAKe,KAAK,CAACuQ,QAAQ,IAAIL,SAAS,GAAGjR,KAAA,CAAKwO,QAAQ,GAAG,IAAI;QACtEqD,SAAS,EAAE7R,KAAA,CAAKD,KAAK,CAAC8N,aAAa,GAAG7N,KAAA,CAAK4N,UAAU,GAAG;MAC1D,CAAC;MAED,IAAIkE,gBAAgB,GAAG;QACrBlM,SAAS,EAAEA,SAAS;QACpB+H,GAAG,EAAE,KAAK;QACVrI,KAAK,EAAEtF,KAAA,CAAKD,KAAK,CAACuF;MACpB,CAAC;MAED,IAAItF,KAAA,CAAKD,KAAK,CAAC+F,OAAO,EAAE;QACtBoL,SAAS,GAAG;UAAEtL,SAAS,EAAE;QAAa,CAAC;QACvCkM,gBAAgB,GAAG;UAAElM,SAAS,EAATA,SAAS;UAAEN,KAAK,EAAEtF,KAAA,CAAKD,KAAK,CAACuF;QAAM,CAAC;MAC3D;MACA,oBACEf,6CAAA,CAAAoB,aAAA,QAASmM,gBAAgB,EACtB,CAAC9R,KAAA,CAAKD,KAAK,CAAC+F,OAAO,GAAGuK,SAAS,GAAG,EAAE,eACrC9L,6CAAA,CAAAoB,aAAA,QAAAK,qEAAA;QAAK7F,GAAG,EAAEH,KAAA,CAAK+R;MAAe,GAAKb,SAAS,gBAC1C3M,6CAAA,CAAAoB,aAAA,CAACqM,6CAAK,EAAAhM,qEAAA;QAAC7F,GAAG,EAAEH,KAAA,CAAKiS;MAAgB,GAAK5C,UAAU,GAC7CrP,KAAA,CAAKD,KAAK,CAACuE,QACP,CACJ,CAAC,EACL,CAACtE,KAAA,CAAKD,KAAK,CAAC+F,OAAO,GAAGwK,SAAS,GAAG,EAAE,EACpC,CAACtQ,KAAA,CAAKD,KAAK,CAAC+F,OAAO,GAAGgK,IAAI,GAAG,EAC3B,CAAC;IAEV,CAAC;IAjuBC9P,KAAA,CAAKqG,IAAI,GAAG,IAAI;IAChBrG,KAAA,CAAKsG,KAAK,GAAG,IAAI;IACjBtG,KAAA,CAAKe,KAAK,GAAA6C,2EAAA,CAAAA,2EAAA,KACLsO,uDAAY;MACfxL,YAAY,EAAE1G,KAAA,CAAKD,KAAK,CAACoS,YAAY;MACrC9E,WAAW,EAAErN,KAAA,CAAKD,KAAK,CAACoS,YAAY,GAAGnS,KAAA,CAAKD,KAAK,CAACoS,YAAY,GAAG,CAAC;MAClE5G,UAAU,EAAEhH,6CAAK,CAACC,QAAQ,CAACsF,KAAK,CAAC9J,KAAA,CAAKD,KAAK,CAACuE,QAAQ;IAAC,EACtD;IACDtE,KAAA,CAAKiI,cAAc,GAAG,EAAE;IACxBjI,KAAA,CAAKwN,SAAS,GAAG,IAAI;IACrBxN,KAAA,CAAKkK,eAAe,GAAG,IAAI;IAC3B,IAAMkI,QAAQ,GAAGpS,KAAA,CAAKqS,OAAO,CAAC,CAAC;IAC/BrS,KAAA,CAAKe,KAAK,GAAA6C,2EAAA,CAAAA,2EAAA,KAAQ5D,KAAA,CAAKe,KAAK,GAAKqR,QAAQ,CAAE;IAAC,OAAApS,KAAA;EAC9C;EAACkB,sEAAA,CAAA6E,WAAA,EAAAjG,gBAAA;EAAA,OAAAqB,yEAAA,CAAA4E,WAAA;IAAA3E,GAAA;IAAAC,KAAA,EAkFD,SAAAwI,eAAeJ,SAAS,EAAE;MACxB,IAAIG,aAAa,GAAG,KAAK;MACzB,SAAA0I,GAAA,MAAAC,YAAA,GAAgBC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC1S,KAAK,CAAC,EAAAuS,GAAA,GAAAC,YAAA,CAAA7R,MAAA,EAAA4R,GAAA,IAAE;QAApC,IAAIlR,GAAG,GAAAmR,YAAA,CAAAD,GAAA;QACV;QACA,IAAI,CAAC7I,SAAS,CAACiJ,cAAc,CAACtR,GAAG,CAAC,EAAE;UAClCwI,aAAa,GAAG,IAAI;UACpB;QACF;QACA,IACE+I,oEAAA,CAAOlJ,SAAS,CAACrI,GAAG,CAAC,MAAK,QAAQ,IAClC,OAAOqI,SAAS,CAACrI,GAAG,CAAC,KAAK,UAAU,IACpCwN,KAAK,CAACnF,SAAS,CAACrI,GAAG,CAAC,CAAC,EACrB;UACA;QACF;QACA,IAAIqI,SAAS,CAACrI,GAAG,CAAC,KAAK,IAAI,CAACrB,KAAK,CAACqB,GAAG,CAAC,EAAE;UACtCwI,aAAa,GAAG,IAAI;UACpB;QACF;MACF;MACA,OACEA,aAAa,IACbrF,6CAAK,CAACC,QAAQ,CAACsF,KAAK,CAAC,IAAI,CAAC/J,KAAK,CAACuE,QAAQ,CAAC,KACvCC,6CAAK,CAACC,QAAQ,CAACsF,KAAK,CAACL,SAAS,CAACnF,QAAQ,CAAC;IAE9C;EAAC;AAAA,EA3H8BC,6CAAK,CAAC4B,SAAS,E;;;;;;AC7BhD,mCAAmC,mBAAO,CAAC,EAAmC;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,6BAA6B;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wH;;;;;;AChBA;AACA;AACA;AACA;AACA;AACA,aAAa,uBAAuB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,6H;;;;;;;ACZA;AAAA,IAAM+L,YAAY,GAAG;EACnBnK,SAAS,EAAE,KAAK;EAChBiH,WAAW,EAAE,IAAI;EACjB4D,gBAAgB,EAAE,CAAC;EACnBC,WAAW,EAAE,IAAI;EACjBnM,YAAY,EAAE,CAAC;EACfoM,SAAS,EAAE,CAAC;EACZxB,QAAQ,EAAE,KAAK;EACfyB,WAAW,EAAE,KAAK;EAClBC,WAAW,EAAE,KAAK;EAClB9L,cAAc,EAAE,EAAE;EAClB0J,UAAU,EAAE,IAAI;EAChBqC,SAAS,EAAE,IAAI;EACfC,SAAS,EAAE,KAAK;EAChB3H,UAAU,EAAE,IAAI;EAChB4H,WAAW,EAAE,IAAI;EACjBxH,UAAU,EAAE,IAAI;EAChByH,SAAS,EAAE,IAAI;EACfC,MAAM,EAAE,KAAK;EAAE;EACfC,OAAO,EAAE,KAAK;EACdC,WAAW,EAAE;IAAEC,MAAM,EAAE,CAAC;IAAEC,MAAM,EAAE,CAAC;IAAEC,IAAI,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAE,CAAC;EACvD3I,UAAU,EAAE,CAAC,CAAC;EACdE,UAAU,EAAE,CAAC;EACbmC,WAAW,EAAE;AACf,CAAC;AAEc6E,2EAAY,E;;;;;;;AC1B3B;AAAA;AAAA;AAAA;;AAEA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB;AACA,WAAW,SAAS;AACpB;AACA,WAAW,OAAO;AAClB,WAAW,QAAQ;AACnB;AACA;AACA;AACA,WAAW,QAAQ;AACnB;AACA;AACA,WAAW,QAAQ;AACnB;AACA;AACA,aAAa,SAAS;AACtB;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA,wBAAwB;;AAExB,mBAAmB;;AAEnB;AACA;AACA;AACA;AACA,GAAG;;;AAGH;AACA,6BAA6B;AAC7B;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA,6EAA6E,aAAa;AAC1F;AACA;;AAEA;AACA;;AAEA;AACA;AACA,KAAK;;;AAGL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,0BAA0B;;AAE1B;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,WAAW,SAAS;AACpB;AACA,WAAW,OAAO;AAClB,WAAW,QAAQ;AACnB;AACA;AACA;AACA,aAAa,SAAS;AACtB;;AAEA;AACA,0BAA0B;AAC1B;AACA;;AAEA;AACA;AACA,GAAG;AACH;;AAE8B;AAC9B;;;;;;;AC3KA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,gBAAgB;;AAEhB;AACA;;AAEA,iBAAiB,sBAAsB;AACvC;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA,KAAK,KAA6B;AAClC;AACA;AACA,EAAE,UAAU,IAA4E;AACxF;AACA,EAAE,iCAAqB,EAAE,mCAAE;AAC3B;AACA,GAAG;AAAA,oGAAC;AACJ,EAAE,MAAM,EAEN;AACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5EyB;AACkB;AAErC,SAAS0B,KAAKA,CAACC,MAAM,EAAEC,UAAU,EAAEC,UAAU,EAAE;EACpD,OAAOC,IAAI,CAACC,GAAG,CAACH,UAAU,EAAEE,IAAI,CAACE,GAAG,CAACL,MAAM,EAAEE,UAAU,CAAC,CAAC;AAC3D;AAEO,IAAMI,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIpG,KAAK,EAAK;EAC3C,IAAMqG,aAAa,GAAG,CAAC,cAAc,EAAE,aAAa,EAAE,SAAS,CAAC;EAChE,IAAI,CAACA,aAAa,CAACC,QAAQ,CAACtG,KAAK,CAACuG,UAAU,CAAC,EAAE;IAC7CvG,KAAK,CAACL,cAAc,CAAC,CAAC;EACxB;AACF,CAAC;AAEM,IAAM1G,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAII,IAAI,EAAK;EAC7C,IAAImN,cAAc,GAAG,EAAE;EACvB,IAAIC,UAAU,GAAGC,cAAc,CAACrN,IAAI,CAAC;EACrC,IAAIsN,QAAQ,GAAGC,YAAY,CAACvN,IAAI,CAAC;EACjC,KAAK,IAAIwD,UAAU,GAAG4J,UAAU,EAAE5J,UAAU,GAAG8J,QAAQ,EAAE9J,UAAU,EAAE,EAAE;IACrE,IAAIxD,IAAI,CAACF,cAAc,CAACqF,OAAO,CAAC3B,UAAU,CAAC,GAAG,CAAC,EAAE;MAC/C2J,cAAc,CAACvS,IAAI,CAAC4I,UAAU,CAAC;IACjC;EACF;EACA,OAAO2J,cAAc;AACvB,CAAC;;AAED;AACO,IAAMK,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAIxN,IAAI,EAAK;EAC7C,IAAIyN,cAAc,GAAG,EAAE;EACvB,IAAIL,UAAU,GAAGC,cAAc,CAACrN,IAAI,CAAC;EACrC,IAAIsN,QAAQ,GAAGC,YAAY,CAACvN,IAAI,CAAC;EACjC,KAAK,IAAIwD,UAAU,GAAG4J,UAAU,EAAE5J,UAAU,GAAG8J,QAAQ,EAAE9J,UAAU,EAAE,EAAE;IACrEiK,cAAc,CAAC7S,IAAI,CAAC4I,UAAU,CAAC;EACjC;EACA,OAAOiK,cAAc;AACvB,CAAC;;AAED;AACO,IAAMJ,cAAc,GAAG,SAAjBA,cAAcA,CAAIrN,IAAI;EAAA,OACjCA,IAAI,CAACV,YAAY,GAAGoO,gBAAgB,CAAC1N,IAAI,CAAC;AAAA;AACrC,IAAMuN,YAAY,GAAG,SAAfA,YAAYA,CAAIvN,IAAI;EAAA,OAC/BA,IAAI,CAACV,YAAY,GAAGqO,iBAAiB,CAAC3N,IAAI,CAAC;AAAA;AACtC,IAAM0N,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAI1N,IAAI;EAAA,OACnCA,IAAI,CAACtD,UAAU,GACXkQ,IAAI,CAACgB,KAAK,CAAC5N,IAAI,CAAC/C,YAAY,GAAG,CAAC,CAAC,IAChC4Q,QAAQ,CAAC7N,IAAI,CAAC2J,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAC1C,CAAC;AAAA;AACA,IAAMgE,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAI3N,IAAI;EAAA,OACpCA,IAAI,CAACtD,UAAU,GACXkQ,IAAI,CAACgB,KAAK,CAAC,CAAC5N,IAAI,CAAC/C,YAAY,GAAG,CAAC,IAAI,CAAC,CAAC,GACvC,CAAC,IACA4Q,QAAQ,CAAC7N,IAAI,CAAC2J,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAC1C3J,IAAI,CAAC/C,YAAY;AAAA;;AAEvB;AACO,IAAM6Q,QAAQ,GAAG,SAAXA,QAAQA,CAAI1O,IAAI;EAAA,OAAMA,IAAI,IAAIA,IAAI,CAAC2O,WAAW,IAAK,CAAC;AAAA;AAC1D,IAAMvO,SAAS,GAAG,SAAZA,SAASA,CAAIJ,IAAI;EAAA,OAAMA,IAAI,IAAIA,IAAI,CAAC4O,YAAY,IAAK,CAAC;AAAA;AAC5D,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAI9B,WAAW,EAA8B;EAAA,IAA5BrF,eAAe,GAAAzN,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EACpE,IAAI6U,KAAK,EAAEC,KAAK,EAAEC,CAAC,EAAEC,UAAU;EAC/BH,KAAK,GAAG/B,WAAW,CAACC,MAAM,GAAGD,WAAW,CAACG,IAAI;EAC7C6B,KAAK,GAAGhC,WAAW,CAACE,MAAM,GAAGF,WAAW,CAACI,IAAI;EAC7C6B,CAAC,GAAGxB,IAAI,CAAC0B,KAAK,CAACH,KAAK,EAAED,KAAK,CAAC;EAC5BG,UAAU,GAAGzB,IAAI,CAAC2B,KAAK,CAAEH,CAAC,GAAG,GAAG,GAAIxB,IAAI,CAAC4B,EAAE,CAAC;EAC5C,IAAIH,UAAU,GAAG,CAAC,EAAE;IAClBA,UAAU,GAAG,GAAG,GAAGzB,IAAI,CAAC6B,GAAG,CAACJ,UAAU,CAAC;EACzC;EACA,IACGA,UAAU,IAAI,EAAE,IAAIA,UAAU,IAAI,CAAC,IACnCA,UAAU,IAAI,GAAG,IAAIA,UAAU,IAAI,GAAI,EACxC;IACA,OAAO,MAAM;EACf;EACA,IAAIA,UAAU,IAAI,GAAG,IAAIA,UAAU,IAAI,GAAG,EAAE;IAC1C,OAAO,OAAO;EAChB;EACA,IAAIvH,eAAe,KAAK,IAAI,EAAE;IAC5B,IAAIuH,UAAU,IAAI,EAAE,IAAIA,UAAU,IAAI,GAAG,EAAE;MACzC,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,MAAM;IACf;EACF;EAEA,OAAO,UAAU;AACnB,CAAC;;AAED;AACO,IAAM3G,SAAS,GAAG,SAAZA,SAASA,CAAI1H,IAAI,EAAK;EACjC,IAAI0O,KAAK,GAAG,IAAI;EAChB,IAAI,CAAC1O,IAAI,CAACvB,QAAQ,EAAE;IAClB,IAAIuB,IAAI,CAACtD,UAAU,IAAIsD,IAAI,CAACV,YAAY,IAAIU,IAAI,CAACmE,UAAU,GAAG,CAAC,EAAE;MAC/DuK,KAAK,GAAG,KAAK;IACf,CAAC,MAAM,IACL1O,IAAI,CAACmE,UAAU,IAAInE,IAAI,CAAC/C,YAAY,IACpC+C,IAAI,CAACV,YAAY,IAAIU,IAAI,CAACmE,UAAU,GAAGnE,IAAI,CAAC/C,YAAY,EACxD;MACAyR,KAAK,GAAG,KAAK;IACf;EACF;EACA,OAAOA,KAAK;AACd,CAAC;;AAED;AACO,IAAMxG,aAAa,GAAG,SAAhBA,aAAaA,CAAIlI,IAAI,EAAEqL,IAAI,EAAK;EAC3C,IAAIsD,SAAS,GAAG,CAAC,CAAC;EAClBtD,IAAI,CAAC/P,OAAO,CAAC,UAACtB,GAAG;IAAA,OAAM2U,SAAS,CAAC3U,GAAG,CAAC,GAAGgG,IAAI,CAAChG,GAAG,CAAC;EAAA,CAAC,CAAC;EACnD,OAAO2U,SAAS;AAClB,CAAC;;AAED;AACO,IAAMpL,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIvD,IAAI,EAAK;EACxC;EACA,IAAImE,UAAU,GAAGhH,4CAAK,CAACC,QAAQ,CAACsF,KAAK,CAAC1C,IAAI,CAAC9C,QAAQ,CAAC;EACpD,IAAM0R,QAAQ,GAAG5O,IAAI,CAACC,OAAO;EAC7B,IAAI4L,SAAS,GAAGe,IAAI,CAACiC,IAAI,CAACf,QAAQ,CAACc,QAAQ,CAAC,CAAC;EAC7C,IAAME,SAAS,GAAG9O,IAAI,CAACE,QAAQ,IAAIF,IAAI,CAACE,QAAQ,CAACkD,IAAI;EACrD,IAAIU,UAAU,GAAG8I,IAAI,CAACiC,IAAI,CAACf,QAAQ,CAACgB,SAAS,CAAC,CAAC;EAC/C,IAAIvK,UAAU;EACd,IAAI,CAACvE,IAAI,CAACgI,QAAQ,EAAE;IAClB,IAAI+G,gBAAgB,GAAG/O,IAAI,CAACtD,UAAU,IAAImR,QAAQ,CAAC7N,IAAI,CAAC2J,aAAa,CAAC,GAAG,CAAC;IAC1E,IACE,OAAO3J,IAAI,CAAC2J,aAAa,KAAK,QAAQ,IACtC3J,IAAI,CAAC2J,aAAa,CAAC7N,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EACpC;MACAiT,gBAAgB,IAAIlD,SAAS,GAAG,GAAG;IACrC;IACAtH,UAAU,GAAGqI,IAAI,CAACiC,IAAI,CAAC,CAAChD,SAAS,GAAGkD,gBAAgB,IAAI/O,IAAI,CAAC/C,YAAY,CAAC;EAC5E,CAAC,MAAM;IACLsH,UAAU,GAAGsH,SAAS;EACxB;EACA,IAAIE,WAAW,GACb6C,QAAQ,IAAIpP,SAAS,CAACoP,QAAQ,CAACvP,aAAa,CAAC,kBAAkB,CAAC,CAAC;EACnE,IAAImK,UAAU,GAAGuC,WAAW,GAAG/L,IAAI,CAAC/C,YAAY;EAChD,IAAIqC,YAAY,GACdU,IAAI,CAACV,YAAY,KAAK/F,SAAS,GAAGyG,IAAI,CAAC+K,YAAY,GAAG/K,IAAI,CAACV,YAAY;EACzE,IAAIU,IAAI,CAAC0G,GAAG,IAAI1G,IAAI,CAACV,YAAY,KAAK/F,SAAS,EAAE;IAC/C+F,YAAY,GAAG6E,UAAU,GAAG,CAAC,GAAGnE,IAAI,CAAC+K,YAAY;EACnD;EACA,IAAIjL,cAAc,GAAGE,IAAI,CAACF,cAAc,IAAI,EAAE;EAC9C,IAAIH,YAAY,GAAGC,qBAAqB,CAAApD,2EAAA,CAAAA,2EAAA,KACnCwD,IAAI;IACPV,YAAY,EAAZA,YAAY;IACZQ,cAAc,EAAdA;EAAc,EACf,CAAC;EACFA,cAAc,GAAGA,cAAc,CAAC/C,MAAM,CAAC4C,YAAY,CAAC;EAEpD,IAAIhG,KAAK,GAAG;IACVwK,UAAU,EAAVA,UAAU;IACVI,UAAU,EAAVA,UAAU;IACVsH,SAAS,EAATA,SAAS;IACT/H,UAAU,EAAVA,UAAU;IACVxE,YAAY,EAAZA,YAAY;IACZyM,WAAW,EAAXA,WAAW;IACXvC,UAAU,EAAVA,UAAU;IACV1J,cAAc,EAAdA;EACF,CAAC;EAED,IAAIE,IAAI,CAAC4H,WAAW,KAAK,IAAI,IAAI5H,IAAI,CAACK,QAAQ,EAAE;IAC9C1G,KAAK,CAAC,aAAa,CAAC,GAAG,SAAS;EAClC;EAEA,OAAOA,KAAK;AACd,CAAC;AAEM,IAAM8L,YAAY,GAAG,SAAfA,YAAYA,CAAIzF,IAAI,EAAK;EACpC,IACE4F,cAAc,GAYZ5F,IAAI,CAZN4F,cAAc;IACdjF,SAAS,GAWPX,IAAI,CAXNW,SAAS;IACT3D,IAAI,GAUFgD,IAAI,CAVNhD,IAAI;IACJyB,QAAQ,GASNuB,IAAI,CATNvB,QAAQ;IACRlD,KAAK,GAQHyE,IAAI,CARNzE,KAAK;IACL4I,UAAU,GAORnE,IAAI,CAPNmE,UAAU;IACVzE,QAAQ,GAMNM,IAAI,CANNN,QAAQ;IACRJ,YAAY,GAKVU,IAAI,CALNV,YAAY;IACZ5C,UAAU,GAIRsD,IAAI,CAJNtD,UAAU;IACVC,cAAc,GAGZqD,IAAI,CAHNrD,cAAc;IACdM,YAAY,GAEV+C,IAAI,CAFN/C,YAAY;IACZyI,MAAM,GACJ1F,IAAI,CADN0F,MAAM;EAER,IAAM5F,cAAc,GAAKE,IAAI,CAAvBF,cAAc;EACpB,IAAI8F,cAAc,IAAIjF,SAAS,EAAE,OAAO,CAAC,CAAC;EAC1C,IAAIqO,cAAc,GAAGzT,KAAK;IACxB0T,UAAU;IACVC,aAAa;IACbC,SAAS;EACX,IAAIxV,KAAK,GAAG,CAAC,CAAC;IACZgM,SAAS,GAAG,CAAC,CAAC;EAChB,IAAMM,WAAW,GAAGxH,QAAQ,GAAGlD,KAAK,GAAGiR,KAAK,CAACjR,KAAK,EAAE,CAAC,EAAE4I,UAAU,GAAG,CAAC,CAAC;EACtE,IAAInH,IAAI,EAAE;IACR,IAAI,CAACyB,QAAQ,KAAKlD,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI4I,UAAU,CAAC,EAAE,OAAO,CAAC,CAAC;IAC9D,IAAI5I,KAAK,GAAG,CAAC,EAAE;MACbyT,cAAc,GAAGzT,KAAK,GAAG4I,UAAU;IACrC,CAAC,MAAM,IAAI5I,KAAK,IAAI4I,UAAU,EAAE;MAC9B6K,cAAc,GAAGzT,KAAK,GAAG4I,UAAU;IACrC;IACA,IAAIzE,QAAQ,IAAII,cAAc,CAACqF,OAAO,CAAC6J,cAAc,CAAC,GAAG,CAAC,EAAE;MAC1DlP,cAAc,GAAGA,cAAc,CAAC/C,MAAM,CAACiS,cAAc,CAAC;IACxD;IACArV,KAAK,GAAG;MACNgH,SAAS,EAAE,IAAI;MACfrB,YAAY,EAAE0P,cAAc;MAC5BlP,cAAc,EAAdA,cAAc;MACdmG,WAAW,EAAE+I;IACf,CAAC;IACDrJ,SAAS,GAAG;MAAEhF,SAAS,EAAE,KAAK;MAAEsF,WAAW,EAAE+I;IAAe,CAAC;EAC/D,CAAC,MAAM;IACLC,UAAU,GAAGD,cAAc;IAC3B,IAAIA,cAAc,GAAG,CAAC,EAAE;MACtBC,UAAU,GAAGD,cAAc,GAAG7K,UAAU;MACxC,IAAI,CAAC1F,QAAQ,EAAEwQ,UAAU,GAAG,CAAC,CAAC,KACzB,IAAI9K,UAAU,GAAGxH,cAAc,KAAK,CAAC,EACxCsS,UAAU,GAAG9K,UAAU,GAAIA,UAAU,GAAGxH,cAAe;IAC3D,CAAC,MAAM,IAAI,CAAC+K,SAAS,CAAC1H,IAAI,CAAC,IAAIgP,cAAc,GAAG1P,YAAY,EAAE;MAC5D0P,cAAc,GAAGC,UAAU,GAAG3P,YAAY;IAC5C,CAAC,MAAM,IAAI5C,UAAU,IAAIsS,cAAc,IAAI7K,UAAU,EAAE;MACrD6K,cAAc,GAAGvQ,QAAQ,GAAG0F,UAAU,GAAGA,UAAU,GAAG,CAAC;MACvD8K,UAAU,GAAGxQ,QAAQ,GAAG,CAAC,GAAG0F,UAAU,GAAG,CAAC;IAC5C,CAAC,MAAM,IAAI6K,cAAc,IAAI7K,UAAU,EAAE;MACvC8K,UAAU,GAAGD,cAAc,GAAG7K,UAAU;MACxC,IAAI,CAAC1F,QAAQ,EAAEwQ,UAAU,GAAG9K,UAAU,GAAGlH,YAAY,CAAC,KACjD,IAAIkH,UAAU,GAAGxH,cAAc,KAAK,CAAC,EAAEsS,UAAU,GAAG,CAAC;IAC5D;IAEA,IAAI,CAACxQ,QAAQ,IAAIuQ,cAAc,GAAG/R,YAAY,IAAIkH,UAAU,EAAE;MAC5D8K,UAAU,GAAG9K,UAAU,GAAGlH,YAAY;IACxC;IAEAiS,aAAa,GAAGxL,YAAY,CAAAlH,2EAAA,CAAAA,2EAAA,KAAMwD,IAAI;MAAEwD,UAAU,EAAEwL;IAAc,EAAE,CAAC;IACrEG,SAAS,GAAGzL,YAAY,CAAAlH,2EAAA,CAAAA,2EAAA,KAAMwD,IAAI;MAAEwD,UAAU,EAAEyL;IAAU,EAAE,CAAC;IAC7D,IAAI,CAACxQ,QAAQ,EAAE;MACb,IAAIyQ,aAAa,KAAKC,SAAS,EAAEH,cAAc,GAAGC,UAAU;MAC5DC,aAAa,GAAGC,SAAS;IAC3B;IACA,IAAIzP,QAAQ,EAAE;MACZI,cAAc,GAAGA,cAAc,CAAC/C,MAAM,CACpC6C,qBAAqB,CAAApD,2EAAA,CAAAA,2EAAA,KAAMwD,IAAI;QAAEV,YAAY,EAAE0P;MAAc,EAAE,CACjE,CAAC;IACH;IACA,IAAI,CAACtJ,MAAM,EAAE;MACX/L,KAAK,GAAG;QACN2F,YAAY,EAAE2P,UAAU;QACxBrL,UAAU,EAAEC,WAAW,CAAArH,2EAAA,CAAAA,2EAAA,KAAMwD,IAAI;UAAE2D,IAAI,EAAEwL;QAAS,EAAE,CAAC;QACrDrP,cAAc,EAAdA,cAAc;QACdmG,WAAW,EAAXA;MACF,CAAC;IACH,CAAC,MAAM;MACLtM,KAAK,GAAG;QACNgH,SAAS,EAAE,IAAI;QACfrB,YAAY,EAAE2P,UAAU;QACxBrL,UAAU,EAAEwL,kBAAkB,CAAA5S,2EAAA,CAAAA,2EAAA,KAAMwD,IAAI;UAAE2D,IAAI,EAAEuL;QAAa,EAAE,CAAC;QAChEpP,cAAc,EAAdA,cAAc;QACdmG,WAAW,EAAXA;MACF,CAAC;MACDN,SAAS,GAAG;QACVhF,SAAS,EAAE,KAAK;QAChBrB,YAAY,EAAE2P,UAAU;QACxBrL,UAAU,EAAEC,WAAW,CAAArH,2EAAA,CAAAA,2EAAA,KAAMwD,IAAI;UAAE2D,IAAI,EAAEwL;QAAS,EAAE,CAAC;QACrDnD,SAAS,EAAE,IAAI;QACf/F,WAAW,EAAXA;MACF,CAAC;IACH;EACF;EACA,OAAO;IAAEtM,KAAK,EAALA,KAAK;IAAEgM,SAAS,EAATA;EAAU,CAAC;AAC7B,CAAC;AAEM,IAAMhD,WAAW,GAAG,SAAdA,WAAWA,CAAI3C,IAAI,EAAEgG,OAAO,EAAK;EAC5C,IAAIqJ,WAAW,EAAEC,WAAW,EAAEC,WAAW,EAAEC,YAAY,EAAEvJ,WAAW;EACpE,IACEtJ,cAAc,GAOZqD,IAAI,CAPNrD,cAAc;IACdM,YAAY,GAMV+C,IAAI,CANN/C,YAAY;IACZkH,UAAU,GAKRnE,IAAI,CALNmE,UAAU;IACV7E,YAAY,GAIVU,IAAI,CAJNV,YAAY;IACCmQ,mBAAmB,GAG9BzP,IAAI,CAHNiG,WAAW;IACXvG,QAAQ,GAENM,IAAI,CAFNN,QAAQ;IACRjB,QAAQ,GACNuB,IAAI,CADNvB,QAAQ;EAEV+Q,YAAY,GAAGrL,UAAU,GAAGxH,cAAc,KAAK,CAAC;EAChD0S,WAAW,GAAGG,YAAY,GAAG,CAAC,GAAG,CAACrL,UAAU,GAAG7E,YAAY,IAAI3C,cAAc;EAC7E,IAAIqJ,OAAO,CAACpD,OAAO,KAAK,UAAU,EAAE;IAClC2M,WAAW,GACTF,WAAW,KAAK,CAAC,GAAG1S,cAAc,GAAGM,YAAY,GAAGoS,WAAW;IACjEpJ,WAAW,GAAG3G,YAAY,GAAGiQ,WAAW;IACxC,IAAI7P,QAAQ,IAAI,CAACjB,QAAQ,EAAE;MACzB6Q,WAAW,GAAGhQ,YAAY,GAAGiQ,WAAW;MACxCtJ,WAAW,GAAGqJ,WAAW,KAAK,CAAC,CAAC,GAAGnL,UAAU,GAAG,CAAC,GAAGmL,WAAW;IACjE;IACA,IAAI,CAAC7Q,QAAQ,EAAE;MACbwH,WAAW,GAAGwJ,mBAAmB,GAAG9S,cAAc;IACpD;EACF,CAAC,MAAM,IAAIqJ,OAAO,CAACpD,OAAO,KAAK,MAAM,EAAE;IACrC2M,WAAW,GAAGF,WAAW,KAAK,CAAC,GAAG1S,cAAc,GAAG0S,WAAW;IAC9DpJ,WAAW,GAAG3G,YAAY,GAAGiQ,WAAW;IACxC,IAAI7P,QAAQ,IAAI,CAACjB,QAAQ,EAAE;MACzBwH,WAAW,GACR,CAAC3G,YAAY,GAAG3C,cAAc,IAAIwH,UAAU,GAAIkL,WAAW;IAChE;IACA,IAAI,CAAC5Q,QAAQ,EAAE;MACbwH,WAAW,GAAGwJ,mBAAmB,GAAG9S,cAAc;IACpD;EACF,CAAC,MAAM,IAAIqJ,OAAO,CAACpD,OAAO,KAAK,MAAM,EAAE;IACrC;IACAqD,WAAW,GAAGD,OAAO,CAACzK,KAAK,GAAGyK,OAAO,CAACrJ,cAAc;EACtD,CAAC,MAAM,IAAIqJ,OAAO,CAACpD,OAAO,KAAK,UAAU,EAAE;IACzC;IACAqD,WAAW,GAAGD,OAAO,CAACzK,KAAK;IAC3B,IAAIkD,QAAQ,EAAE;MACZ,IAAIiN,SAAS,GAAGgE,gBAAgB,CAAAlT,2EAAA,CAAAA,2EAAA,KAAMwD,IAAI;QAAEiG,WAAW,EAAXA;MAAW,EAAE,CAAC;MAC1D,IAAIA,WAAW,GAAGD,OAAO,CAAC1G,YAAY,IAAIoM,SAAS,KAAK,MAAM,EAAE;QAC9DzF,WAAW,GAAGA,WAAW,GAAG9B,UAAU;MACxC,CAAC,MAAM,IAAI8B,WAAW,GAAGD,OAAO,CAAC1G,YAAY,IAAIoM,SAAS,KAAK,OAAO,EAAE;QACtEzF,WAAW,GAAGA,WAAW,GAAG9B,UAAU;MACxC;IACF;EACF,CAAC,MAAM,IAAI6B,OAAO,CAACpD,OAAO,KAAK,OAAO,EAAE;IACtCqD,WAAW,GAAGsB,MAAM,CAACvB,OAAO,CAACzK,KAAK,CAAC;EACrC;EACA,OAAO0K,WAAW;AACpB,CAAC;AACM,IAAMO,UAAU,GAAG,SAAbA,UAAUA,CAAItO,CAAC,EAAEuO,aAAa,EAAEC,GAAG,EAAK;EACnD,IAAIxO,CAAC,CAACyX,MAAM,CAACC,OAAO,CAACC,KAAK,CAAC,uBAAuB,CAAC,IAAI,CAACpJ,aAAa,EACnE,OAAO,EAAE;EACX,IAAIvO,CAAC,CAAC4X,OAAO,KAAK,EAAE,EAAE,OAAOpJ,GAAG,GAAG,MAAM,GAAG,UAAU;EACtD,IAAIxO,CAAC,CAAC4X,OAAO,KAAK,EAAE,EAAE,OAAOpJ,GAAG,GAAG,UAAU,GAAG,MAAM;EACtD,OAAO,EAAE;AACX,CAAC;AAEM,IAAMM,UAAU,GAAG,SAAbA,UAAUA,CAAI9O,CAAC,EAAE+O,KAAK,EAAEC,SAAS,EAAK;EACjDhP,CAAC,CAACyX,MAAM,CAACC,OAAO,KAAK,KAAK,IAAI7C,kBAAkB,CAAC7U,CAAC,CAAC;EACnD,IAAI,CAAC+O,KAAK,IAAK,CAACC,SAAS,IAAIhP,CAAC,CAAC6X,IAAI,CAAC5K,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAE,EAAE,OAAO,EAAE;EACvE,OAAO;IACL+E,QAAQ,EAAE,IAAI;IACdiC,WAAW,EAAE;MACXC,MAAM,EAAElU,CAAC,CAAC8X,OAAO,GAAG9X,CAAC,CAAC8X,OAAO,CAAC,CAAC,CAAC,CAACC,KAAK,GAAG/X,CAAC,CAACgY,OAAO;MAClD7D,MAAM,EAAEnU,CAAC,CAAC8X,OAAO,GAAG9X,CAAC,CAAC8X,OAAO,CAAC,CAAC,CAAC,CAACG,KAAK,GAAGjY,CAAC,CAACkY,OAAO;MAClD9D,IAAI,EAAEpU,CAAC,CAAC8X,OAAO,GAAG9X,CAAC,CAAC8X,OAAO,CAAC,CAAC,CAAC,CAACC,KAAK,GAAG/X,CAAC,CAACgY,OAAO;MAChD3D,IAAI,EAAErU,CAAC,CAAC8X,OAAO,GAAG9X,CAAC,CAAC8X,OAAO,CAAC,CAAC,CAAC,CAACG,KAAK,GAAGjY,CAAC,CAACkY;IAC3C;EACF,CAAC;AACH,CAAC;AACM,IAAMjJ,SAAS,GAAG,SAAZA,SAASA,CAAIjP,CAAC,EAAE8H,IAAI,EAAK;EACpC;EACA,IACE8L,SAAS,GAmBP9L,IAAI,CAnBN8L,SAAS;IACTnL,SAAS,GAkBPX,IAAI,CAlBNW,SAAS;IACTqH,QAAQ,GAiBNhI,IAAI,CAjBNgI,QAAQ;IACRqI,YAAY,GAgBVrQ,IAAI,CAhBNqQ,YAAY;IACZvJ,eAAe,GAeb9G,IAAI,CAfN8G,eAAe;IACfJ,GAAG,GAcD1G,IAAI,CAdN0G,GAAG;IACHpH,YAAY,GAaVU,IAAI,CAbNV,YAAY;IACZgR,YAAY,GAYVtQ,IAAI,CAZNsQ,YAAY;IACZ3E,WAAW,GAWT3L,IAAI,CAXN2L,WAAW;IACX4E,MAAM,GAUJvQ,IAAI,CAVNuQ,MAAM;IACNtE,MAAM,GASJjM,IAAI,CATNiM,MAAM;IACNC,OAAO,GAQLlM,IAAI,CARNkM,OAAO;IACP/H,UAAU,GAORnE,IAAI,CAPNmE,UAAU;IACVxH,cAAc,GAMZqD,IAAI,CANNrD,cAAc;IACd8B,QAAQ,GAKNuB,IAAI,CALNvB,QAAQ;IACR0N,WAAW,GAITnM,IAAI,CAJNmM,WAAW;IACXqE,UAAU,GAGRxQ,IAAI,CAHNwQ,UAAU;IACVhH,UAAU,GAERxJ,IAAI,CAFNwJ,UAAU;IACVqC,SAAS,GACP7L,IAAI,CADN6L,SAAS;EAEX,IAAIC,SAAS,EAAE;EACf,IAAInL,SAAS,EAAE,OAAOoM,kBAAkB,CAAC7U,CAAC,CAAC;EAC3C,IAAI8P,QAAQ,IAAIqI,YAAY,IAAIvJ,eAAe,EAAEiG,kBAAkB,CAAC7U,CAAC,CAAC;EACtE,IAAI8T,SAAS;IACXrS,KAAK,GAAG,CAAC,CAAC;EACZ,IAAI8W,OAAO,GAAG/M,YAAY,CAAC1D,IAAI,CAAC;EAChCmM,WAAW,CAACG,IAAI,GAAGpU,CAAC,CAAC8X,OAAO,GAAG9X,CAAC,CAAC8X,OAAO,CAAC,CAAC,CAAC,CAACC,KAAK,GAAG/X,CAAC,CAACgY,OAAO;EAC7D/D,WAAW,CAACI,IAAI,GAAGrU,CAAC,CAAC8X,OAAO,GAAG9X,CAAC,CAAC8X,OAAO,CAAC,CAAC,CAAC,CAACG,KAAK,GAAGjY,CAAC,CAACkY,OAAO;EAC7DjE,WAAW,CAACuE,WAAW,GAAG9D,IAAI,CAAC2B,KAAK,CAClC3B,IAAI,CAAC+D,IAAI,CAAC/D,IAAI,CAACgE,GAAG,CAACzE,WAAW,CAACG,IAAI,GAAGH,WAAW,CAACC,MAAM,EAAE,CAAC,CAAC,CAC9D,CAAC;EACD,IAAIyE,mBAAmB,GAAGjE,IAAI,CAAC2B,KAAK,CAClC3B,IAAI,CAAC+D,IAAI,CAAC/D,IAAI,CAACgE,GAAG,CAACzE,WAAW,CAACI,IAAI,GAAGJ,WAAW,CAACE,MAAM,EAAE,CAAC,CAAC,CAC9D,CAAC;EACD,IAAI,CAACvF,eAAe,IAAI,CAACoF,OAAO,IAAI2E,mBAAmB,GAAG,EAAE,EAAE;IAC5D,OAAO;MAAE/E,SAAS,EAAE;IAAK,CAAC;EAC5B;EACA,IAAIhF,eAAe,EAAEqF,WAAW,CAACuE,WAAW,GAAGG,mBAAmB;EAClE,IAAIC,cAAc,GAChB,CAAC,CAACpK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,KAAKyF,WAAW,CAACG,IAAI,GAAGH,WAAW,CAACC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EACpE,IAAItF,eAAe,EACjBgK,cAAc,GAAG3E,WAAW,CAACI,IAAI,GAAGJ,WAAW,CAACE,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;EAEjE,IAAI0E,QAAQ,GAAGnE,IAAI,CAACiC,IAAI,CAAC1K,UAAU,GAAGxH,cAAc,CAAC;EACrD,IAAIqU,cAAc,GAAG/C,iBAAiB,CAACjO,IAAI,CAACmM,WAAW,EAAErF,eAAe,CAAC;EACzE,IAAImK,gBAAgB,GAAG9E,WAAW,CAACuE,WAAW;EAC9C,IAAI,CAACjS,QAAQ,EAAE;IACb,IACGa,YAAY,KAAK,CAAC,KAChB0R,cAAc,KAAK,OAAO,IAAIA,cAAc,KAAK,MAAM,CAAC,IAC1D1R,YAAY,GAAG,CAAC,IAAIyR,QAAQ,KAC1BC,cAAc,KAAK,MAAM,IAAIA,cAAc,KAAK,IAAI,CAAE,IACxD,CAACtJ,SAAS,CAAC1H,IAAI,CAAC,KACdgR,cAAc,KAAK,MAAM,IAAIA,cAAc,KAAK,IAAI,CAAE,EACzD;MACAC,gBAAgB,GAAG9E,WAAW,CAACuE,WAAW,GAAGJ,YAAY;MACzD,IAAI3E,WAAW,KAAK,KAAK,IAAI4E,MAAM,EAAE;QACnCA,MAAM,CAACS,cAAc,CAAC;QACtBrX,KAAK,CAAC,aAAa,CAAC,GAAG,IAAI;MAC7B;IACF;EACF;EACA,IAAI,CAACsS,MAAM,IAAIuE,UAAU,EAAE;IACzBA,UAAU,CAACQ,cAAc,CAAC;IAC1BrX,KAAK,CAAC,QAAQ,CAAC,GAAG,IAAI;EACxB;EACA,IAAI,CAACqO,QAAQ,EAAE;IACb,IAAI,CAACtB,GAAG,EAAE;MACRsF,SAAS,GAAGyE,OAAO,GAAGQ,gBAAgB,GAAGH,cAAc;IACzD,CAAC,MAAM;MACL9E,SAAS,GAAGyE,OAAO,GAAGQ,gBAAgB,GAAGH,cAAc;IACzD;EACF,CAAC,MAAM;IACL9E,SAAS,GACPyE,OAAO,GAAGQ,gBAAgB,IAAIzH,UAAU,GAAGqC,SAAS,CAAC,GAAGiF,cAAc;EAC1E;EACA,IAAIhK,eAAe,EAAE;IACnBkF,SAAS,GAAGyE,OAAO,GAAGQ,gBAAgB,GAAGH,cAAc;EACzD;EACAnX,KAAK,GAAA6C,2EAAA,CAAAA,2EAAA,KACA7C,KAAK;IACRwS,WAAW,EAAXA,WAAW;IACXH,SAAS,EAATA,SAAS;IACTpI,UAAU,EAAEC,WAAW,CAAArH,2EAAA,CAAAA,2EAAA,KAAMwD,IAAI;MAAE2D,IAAI,EAAEqI;IAAS,EAAE;EAAC,EACtD;EACD,IACEY,IAAI,CAAC6B,GAAG,CAACtC,WAAW,CAACG,IAAI,GAAGH,WAAW,CAACC,MAAM,CAAC,GAC/CQ,IAAI,CAAC6B,GAAG,CAACtC,WAAW,CAACI,IAAI,GAAGJ,WAAW,CAACE,MAAM,CAAC,GAAG,GAAG,EACrD;IACA,OAAO1S,KAAK;EACd;EACA,IAAIwS,WAAW,CAACuE,WAAW,GAAG,EAAE,EAAE;IAChC/W,KAAK,CAAC,SAAS,CAAC,GAAG,IAAI;IACvBoT,kBAAkB,CAAC7U,CAAC,CAAC;EACvB;EACA,OAAOyB,KAAK;AACd,CAAC;AACM,IAAMyN,QAAQ,GAAG,SAAXA,QAAQA,CAAIlP,CAAC,EAAE8H,IAAI,EAAK;EACnC,IACEkK,QAAQ,GAaNlK,IAAI,CAbNkK,QAAQ;IACRjD,KAAK,GAYHjH,IAAI,CAZNiH,KAAK;IACLkF,WAAW,GAWTnM,IAAI,CAXNmM,WAAW;IACXN,SAAS,GAUP7L,IAAI,CAVN6L,SAAS;IACTqF,cAAc,GASZlR,IAAI,CATNkR,cAAc;IACdpK,eAAe,GAQb9G,IAAI,CARN8G,eAAe;IACf0C,UAAU,GAORxJ,IAAI,CAPNwJ,UAAU;IACV6G,YAAY,GAMVrQ,IAAI,CANNqQ,YAAY;IACZvE,SAAS,GAKP9L,IAAI,CALN8L,SAAS;IACTqF,OAAO,GAILnR,IAAI,CAJNmR,OAAO;IACPlL,WAAW,GAGTjG,IAAI,CAHNiG,WAAW;IACX3G,YAAY,GAEVU,IAAI,CAFNV,YAAY;IACZb,QAAQ,GACNuB,IAAI,CADNvB,QAAQ;EAEV,IAAI,CAACyL,QAAQ,EAAE;IACb,IAAIjD,KAAK,EAAE8F,kBAAkB,CAAC7U,CAAC,CAAC;IAChC,OAAO,CAAC,CAAC;EACX;EACA,IAAIkZ,QAAQ,GAAGtK,eAAe,GAC1B0C,UAAU,GAAG0H,cAAc,GAC3BrF,SAAS,GAAGqF,cAAc;EAC9B,IAAIF,cAAc,GAAG/C,iBAAiB,CAAC9B,WAAW,EAAErF,eAAe,CAAC;EACpE;EACA,IAAInN,KAAK,GAAG;IACVuQ,QAAQ,EAAE,KAAK;IACfyB,WAAW,EAAE,KAAK;IAClBG,SAAS,EAAE,KAAK;IAChBI,OAAO,EAAE,KAAK;IACdD,MAAM,EAAE,KAAK;IACbD,SAAS,EAAE,IAAI;IACfG,WAAW,EAAE,CAAC;EAChB,CAAC;EACD,IAAIL,SAAS,EAAE;IACb,OAAOnS,KAAK;EACd;EACA,IAAI,CAACwS,WAAW,CAACuE,WAAW,EAAE;IAC5B,OAAO/W,KAAK;EACd;EACA,IAAIwS,WAAW,CAACuE,WAAW,GAAGU,QAAQ,EAAE;IACtCrE,kBAAkB,CAAC7U,CAAC,CAAC;IACrB,IAAIiZ,OAAO,EAAE;MACXA,OAAO,CAACH,cAAc,CAAC;IACzB;IACA,IAAI7M,UAAU,EAAErG,QAAQ;IACxB,IAAIuT,WAAW,GAAG5S,QAAQ,GAAGa,YAAY,GAAG2G,WAAW;IACvD,QAAQ+K,cAAc;MACpB,KAAK,MAAM;MACX,KAAK,IAAI;QACPlT,QAAQ,GAAGuT,WAAW,GAAGC,aAAa,CAACtR,IAAI,CAAC;QAC5CmE,UAAU,GAAGkM,YAAY,GAAGkB,cAAc,CAACvR,IAAI,EAAElC,QAAQ,CAAC,GAAGA,QAAQ;QACrEnE,KAAK,CAAC,kBAAkB,CAAC,GAAG,CAAC;QAC7B;MACF,KAAK,OAAO;MACZ,KAAK,MAAM;QACTmE,QAAQ,GAAGuT,WAAW,GAAGC,aAAa,CAACtR,IAAI,CAAC;QAC5CmE,UAAU,GAAGkM,YAAY,GAAGkB,cAAc,CAACvR,IAAI,EAAElC,QAAQ,CAAC,GAAGA,QAAQ;QACrEnE,KAAK,CAAC,kBAAkB,CAAC,GAAG,CAAC;QAC7B;MACF;QACEwK,UAAU,GAAGkN,WAAW;IAC5B;IACA1X,KAAK,CAAC,qBAAqB,CAAC,GAAGwK,UAAU;EAC3C,CAAC,MAAM;IACL;IACA,IAAIsH,WAAW,GAAG/H,YAAY,CAAC1D,IAAI,CAAC;IACpCrG,KAAK,CAAC,YAAY,CAAC,GAAGyV,kBAAkB,CAAA5S,2EAAA,CAAAA,2EAAA,KAAMwD,IAAI;MAAE2D,IAAI,EAAE8H;IAAW,EAAE,CAAC;EAC1E;EACA,OAAO9R,KAAK;AACd,CAAC;AACM,IAAM6X,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAIxR,IAAI,EAAK;EAC3C,IAAI6M,GAAG,GAAG7M,IAAI,CAACvB,QAAQ,GAAGuB,IAAI,CAACmE,UAAU,GAAG,CAAC,GAAGnE,IAAI,CAACmE,UAAU;EAC/D,IAAIvK,UAAU,GAAGoG,IAAI,CAACvB,QAAQ,GAAGuB,IAAI,CAAC/C,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;EAC3D,IAAIwU,OAAO,GAAGzR,IAAI,CAACvB,QAAQ,GAAGuB,IAAI,CAAC/C,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;EACxD,IAAIyU,OAAO,GAAG,EAAE;EAChB,OAAO9X,UAAU,GAAGiT,GAAG,EAAE;IACvB6E,OAAO,CAAC9W,IAAI,CAAChB,UAAU,CAAC;IACxBA,UAAU,GAAG6X,OAAO,GAAGzR,IAAI,CAACrD,cAAc;IAC1C8U,OAAO,IAAI7E,IAAI,CAACE,GAAG,CAAC9M,IAAI,CAACrD,cAAc,EAAEqD,IAAI,CAAC/C,YAAY,CAAC;EAC7D;EACA,OAAOyU,OAAO;AAChB,CAAC;AACM,IAAMH,cAAc,GAAG,SAAjBA,cAAcA,CAAIvR,IAAI,EAAEzE,KAAK,EAAK;EAC7C,IAAMoW,UAAU,GAAGH,mBAAmB,CAACxR,IAAI,CAAC;EAC5C,IAAI4R,aAAa,GAAG,CAAC;EACrB,IAAIrW,KAAK,GAAGoW,UAAU,CAACA,UAAU,CAACrY,MAAM,GAAG,CAAC,CAAC,EAAE;IAC7CiC,KAAK,GAAGoW,UAAU,CAACA,UAAU,CAACrY,MAAM,GAAG,CAAC,CAAC;EAC3C,CAAC,MAAM;IACL,KAAK,IAAIuY,CAAC,IAAIF,UAAU,EAAE;MACxB,IAAIpW,KAAK,GAAGoW,UAAU,CAACE,CAAC,CAAC,EAAE;QACzBtW,KAAK,GAAGqW,aAAa;QACrB;MACF;MACAA,aAAa,GAAGD,UAAU,CAACE,CAAC,CAAC;IAC/B;EACF;EACA,OAAOtW,KAAK;AACd,CAAC;AACM,IAAM+V,aAAa,GAAG,SAAhBA,aAAaA,CAAItR,IAAI,EAAK;EACrC,IAAM8R,YAAY,GAAG9R,IAAI,CAACtD,UAAU,GAChCsD,IAAI,CAACuE,UAAU,GAAGqI,IAAI,CAACgB,KAAK,CAAC5N,IAAI,CAAC/C,YAAY,GAAG,CAAC,CAAC,GACnD,CAAC;EACL,IAAI+C,IAAI,CAACqQ,YAAY,EAAE;IACrB,IAAI0B,WAAW;IACf,IAAMC,SAAS,GAAGhS,IAAI,CAACC,OAAO;IAC9B,IAAMgS,MAAM,GACTD,SAAS,CAAC9Q,gBAAgB,IACzB8Q,SAAS,CAAC9Q,gBAAgB,CAAC,cAAc,CAAC,IAC5C,EAAE;IACJC,KAAK,CAAC+Q,IAAI,CAACD,MAAM,CAAC,CAACE,KAAK,CAAC,UAAChZ,KAAK,EAAK;MAClC,IAAI,CAAC6G,IAAI,CAACgI,QAAQ,EAAE;QAClB,IACE7O,KAAK,CAACiZ,UAAU,GAAGN,YAAY,GAAGhE,QAAQ,CAAC3U,KAAK,CAAC,GAAG,CAAC,GACrD6G,IAAI,CAACgM,SAAS,GAAG,CAAC,CAAC,EACnB;UACA+F,WAAW,GAAG5Y,KAAK;UACnB,OAAO,KAAK;QACd;MACF,CAAC,MAAM;QACL,IAAIA,KAAK,CAACkZ,SAAS,GAAG7S,SAAS,CAACrG,KAAK,CAAC,GAAG,CAAC,GAAG6G,IAAI,CAACgM,SAAS,GAAG,CAAC,CAAC,EAAE;UAChE+F,WAAW,GAAG5Y,KAAK;UACnB,OAAO,KAAK;QACd;MACF;MAEA,OAAO,IAAI;IACb,CAAC,CAAC;IAEF,IAAI,CAAC4Y,WAAW,EAAE;MAChB,OAAO,CAAC;IACV;IACA,IAAMO,YAAY,GAChBtS,IAAI,CAAC0G,GAAG,KAAK,IAAI,GACb1G,IAAI,CAACmE,UAAU,GAAGnE,IAAI,CAACV,YAAY,GACnCU,IAAI,CAACV,YAAY;IACvB,IAAMiT,eAAe,GACnB3F,IAAI,CAAC6B,GAAG,CAACsD,WAAW,CAACS,OAAO,CAACjX,KAAK,GAAG+W,YAAY,CAAC,IAAI,CAAC;IACzD,OAAOC,eAAe;EACxB,CAAC,MAAM;IACL,OAAOvS,IAAI,CAACrD,cAAc;EAC5B;AACF,CAAC;AAEM,IAAM8V,aAAa,GAAG,SAAhBA,aAAaA,CAAIzS,IAAI,EAAE0S,SAAS;EAAA;IAC3C;IACAA,SAAS,CAACC,MAAM,CAAC,UAAC1Y,KAAK,EAAED,GAAG;MAAA,OAAKC,KAAK,IAAI+F,IAAI,CAACsL,cAAc,CAACtR,GAAG,CAAC;IAAA,GAAE,IAAI,CAAC,GACrE,IAAI,GACJ6C,OAAO,CAAC+V,KAAK,CAAC,eAAe,EAAE5S,IAAI;EAAC;AAAA;AAEnC,IAAM6D,WAAW,GAAG,SAAdA,WAAWA,CAAI7D,IAAI,EAAK;EACnCyS,aAAa,CAACzS,IAAI,EAAE,CAClB,MAAM,EACN,eAAe,EACf,YAAY,EACZ,cAAc,EACd,YAAY,CACb,CAAC;EACF,IAAI8D,UAAU,EAAE+O,WAAW;EAC3B,IAAI,CAAC7S,IAAI,CAACgI,QAAQ,EAAE;IAClBlE,UAAU,GAAGgP,cAAc,CAAC9S,IAAI,CAAC,GAAGA,IAAI,CAACuE,UAAU;EACrD,CAAC,MAAM;IACL,IAAMwO,aAAa,GAAG/S,IAAI,CAACtB,OAAO,GAC9BsB,IAAI,CAACmE,UAAU,GACfnE,IAAI,CAACmE,UAAU,GAAG,CAAC,GAAGnE,IAAI,CAAC/C,YAAY;IAC3C4V,WAAW,GAAGE,aAAa,GAAG/S,IAAI,CAAC+L,WAAW;EAChD;EACA,IAAI7N,KAAK,GAAG;IACV8U,OAAO,EAAE,CAAC;IACVC,UAAU,EAAE,EAAE;IACdC,gBAAgB,EAAE;EACpB,CAAC;EACD,IAAIlT,IAAI,CAACmT,YAAY,EAAE;IACrB,IAAIC,eAAe,GAAG,CAACpT,IAAI,CAACgI,QAAQ,GAChC,cAAc,GAAGhI,IAAI,CAAC2D,IAAI,GAAG,eAAe,GAC5C,mBAAmB,GAAG3D,IAAI,CAAC2D,IAAI,GAAG,UAAU;IAChD,IAAI0P,SAAS,GAAG,CAACrT,IAAI,CAACgI,QAAQ,GAC1B,cAAc,GAAGhI,IAAI,CAAC2D,IAAI,GAAG,eAAe,GAC5C,mBAAmB,GAAG3D,IAAI,CAAC2D,IAAI,GAAG,UAAU;IAChD,IAAI2P,WAAW,GAAG,CAACtT,IAAI,CAACgI,QAAQ,GAC5B,aAAa,GAAGhI,IAAI,CAAC2D,IAAI,GAAG,KAAK,GACjC,aAAa,GAAG3D,IAAI,CAAC2D,IAAI,GAAG,KAAK;IACrCzF,KAAK,GAAA1B,2EAAA,CAAAA,2EAAA,KACA0B,KAAK;MACRkV,eAAe,EAAfA,eAAe;MACfC,SAAS,EAATA,SAAS;MACTC,WAAW,EAAXA;IAAW,EACZ;EACH,CAAC,MAAM;IACL,IAAItT,IAAI,CAACgI,QAAQ,EAAE;MACjB9J,KAAK,CAAC,KAAK,CAAC,GAAG8B,IAAI,CAAC2D,IAAI;IAC1B,CAAC,MAAM;MACLzF,KAAK,CAAC,MAAM,CAAC,GAAG8B,IAAI,CAAC2D,IAAI;IAC3B;EACF;EACA,IAAI3D,IAAI,CAAChD,IAAI,EAAEkB,KAAK,GAAG;IAAE8U,OAAO,EAAE;EAAE,CAAC;EACrC,IAAIlP,UAAU,EAAE5F,KAAK,CAACC,KAAK,GAAG2F,UAAU;EACxC,IAAI+O,WAAW,EAAE3U,KAAK,CAACqB,MAAM,GAAGsT,WAAW;;EAE3C;EACA,IAAIvY,MAAM,IAAI,CAACA,MAAM,CAACqH,gBAAgB,IAAIrH,MAAM,CAACsH,WAAW,EAAE;IAC5D,IAAI,CAAC5B,IAAI,CAACgI,QAAQ,EAAE;MAClB9J,KAAK,CAACqV,UAAU,GAAGvT,IAAI,CAAC2D,IAAI,GAAG,IAAI;IACrC,CAAC,MAAM;MACLzF,KAAK,CAACsV,SAAS,GAAGxT,IAAI,CAAC2D,IAAI,GAAG,IAAI;IACpC;EACF;EAEA,OAAOzF,KAAK;AACd,CAAC;AACM,IAAMkR,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIpP,IAAI,EAAK;EAC1CyS,aAAa,CAACzS,IAAI,EAAE,CAClB,MAAM,EACN,eAAe,EACf,YAAY,EACZ,cAAc,EACd,YAAY,EACZ,OAAO,EACP,SAAS,CACV,CAAC;EACF,IAAI9B,KAAK,GAAG2F,WAAW,CAAC7D,IAAI,CAAC;EAC7B;EACA,IAAIA,IAAI,CAACmT,YAAY,EAAE;IACrBjV,KAAK,CAACgV,gBAAgB,GACpB,oBAAoB,GAAGlT,IAAI,CAACe,KAAK,GAAG,KAAK,GAAGf,IAAI,CAACyT,OAAO;IAC1DvV,KAAK,CAAC+U,UAAU,GAAG,YAAY,GAAGjT,IAAI,CAACe,KAAK,GAAG,KAAK,GAAGf,IAAI,CAACyT,OAAO;EACrE,CAAC,MAAM;IACL,IAAIzT,IAAI,CAACgI,QAAQ,EAAE;MACjB9J,KAAK,CAAC+U,UAAU,GAAG,MAAM,GAAGjT,IAAI,CAACe,KAAK,GAAG,KAAK,GAAGf,IAAI,CAACyT,OAAO;IAC/D,CAAC,MAAM;MACLvV,KAAK,CAAC+U,UAAU,GAAG,OAAO,GAAGjT,IAAI,CAACe,KAAK,GAAG,KAAK,GAAGf,IAAI,CAACyT,OAAO;IAChE;EACF;EACA,OAAOvV,KAAK;AACd,CAAC;AACM,IAAMwF,YAAY,GAAG,SAAfA,YAAYA,CAAI1D,IAAI,EAAK;EACpC,IAAIA,IAAI,CAACtB,OAAO,EAAE;IAChB,OAAO,CAAC;EACV;EAEA+T,aAAa,CAACzS,IAAI,EAAE,CAClB,YAAY,EACZ,UAAU,EACV,UAAU,EACV,YAAY,EACZ,YAAY,EACZ,cAAc,EACd,gBAAgB,EAChB,YAAY,EACZ,WAAW,EACX,eAAe,EACf,aAAa,CACd,CAAC;EAEF,IACEwD,UAAU,GAaRxD,IAAI,CAbNwD,UAAU;IACVtD,QAAQ,GAYNF,IAAI,CAZNE,QAAQ;IACRzB,QAAQ,GAWNuB,IAAI,CAXNvB,QAAQ;IACR/B,UAAU,GAURsD,IAAI,CAVNtD,UAAU;IACVyH,UAAU,GASRnE,IAAI,CATNmE,UAAU;IACVlH,YAAY,GAQV+C,IAAI,CARN/C,YAAY;IACZN,cAAc,GAOZqD,IAAI,CAPNrD,cAAc;IACd4H,UAAU,GAMRvE,IAAI,CANNuE,UAAU;IACVsH,SAAS,GAKP7L,IAAI,CALN6L,SAAS;IACTrO,aAAa,GAIXwC,IAAI,CAJNxC,aAAa;IACbuO,WAAW,GAGT/L,IAAI,CAHN+L,WAAW;IACX/O,IAAI,GAEFgD,IAAI,CAFNhD,IAAI;IACJgL,QAAQ,GACNhI,IAAI,CADNgI,QAAQ;EAGV,IAAIuH,WAAW,GAAG,CAAC;EACnB,IAAI9L,UAAU;EACd,IAAIwC,WAAW;EACf,IAAIyN,cAAc,GAAG,CAAC;EAEtB,IAAI1W,IAAI,IAAIgD,IAAI,CAACmE,UAAU,KAAK,CAAC,EAAE;IACjC,OAAO,CAAC;EACV;EAEA,IAAIwP,cAAc,GAAG,CAAC;EACtB,IAAIlV,QAAQ,EAAE;IACZkV,cAAc,GAAG,CAACzP,YAAY,CAAClE,IAAI,CAAC,CAAC,CAAC;IACtC;IACA,IACEmE,UAAU,GAAGxH,cAAc,KAAK,CAAC,IACjC6G,UAAU,GAAG7G,cAAc,GAAGwH,UAAU,EACxC;MACAwP,cAAc,GAAG,EAAEnQ,UAAU,GAAGW,UAAU,GACtClH,YAAY,IAAIuG,UAAU,GAAGW,UAAU,CAAC,GACxCA,UAAU,GAAGxH,cAAc,CAAC;IAClC;IACA;IACA,IAAID,UAAU,EAAE;MACdiX,cAAc,IAAI9F,QAAQ,CAAC5Q,YAAY,GAAG,CAAC,CAAC;IAC9C;EACF,CAAC,MAAM;IACL,IACEkH,UAAU,GAAGxH,cAAc,KAAK,CAAC,IACjC6G,UAAU,GAAG7G,cAAc,GAAGwH,UAAU,EACxC;MACAwP,cAAc,GAAG1W,YAAY,GAAIkH,UAAU,GAAGxH,cAAe;IAC/D;IACA,IAAID,UAAU,EAAE;MACdiX,cAAc,GAAG9F,QAAQ,CAAC5Q,YAAY,GAAG,CAAC,CAAC;IAC7C;EACF;EACAsS,WAAW,GAAGoE,cAAc,GAAGpP,UAAU;EACzCmP,cAAc,GAAGC,cAAc,GAAG5H,WAAW;EAE7C,IAAI,CAAC/D,QAAQ,EAAE;IACbvE,UAAU,GAAGD,UAAU,GAAGe,UAAU,GAAG,CAAC,CAAC,GAAGgL,WAAW;EACzD,CAAC,MAAM;IACL9L,UAAU,GAAGD,UAAU,GAAGuI,WAAW,GAAG,CAAC,CAAC,GAAG2H,cAAc;EAC7D;EAEA,IAAIlW,aAAa,KAAK,IAAI,EAAE;IAC1B,IAAIoW,gBAAgB;IACpB,IAAMC,SAAS,GAAG3T,QAAQ,IAAIA,QAAQ,CAACkD,IAAI;IAC3CwQ,gBAAgB,GAAGpQ,UAAU,GAAGU,YAAY,CAAClE,IAAI,CAAC;IAClDiG,WAAW,GAAG4N,SAAS,IAAIA,SAAS,CAACC,UAAU,CAACF,gBAAgB,CAAC;IACjEnQ,UAAU,GAAGwC,WAAW,GAAGA,WAAW,CAACmM,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC;IAC1D,IAAI1V,UAAU,KAAK,IAAI,EAAE;MACvBkX,gBAAgB,GAAGnV,QAAQ,GACvB+E,UAAU,GAAGU,YAAY,CAAClE,IAAI,CAAC,GAC/BwD,UAAU;MACdyC,WAAW,GAAG4N,SAAS,IAAIA,SAAS,CAAC3W,QAAQ,CAAC0W,gBAAgB,CAAC;MAC/DnQ,UAAU,GAAG,CAAC;MACd,KAAK,IAAItK,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGya,gBAAgB,EAAEza,KAAK,EAAE,EAAE;QACrDsK,UAAU,IACRoQ,SAAS,IACTA,SAAS,CAAC3W,QAAQ,CAAC/D,KAAK,CAAC,IACzB0a,SAAS,CAAC3W,QAAQ,CAAC/D,KAAK,CAAC,CAAC4U,WAAW;MACzC;MACAtK,UAAU,IAAIoK,QAAQ,CAAC7N,IAAI,CAAC2J,aAAa,CAAC;MAC1ClG,UAAU,IAAIwC,WAAW,IAAI,CAAC4F,SAAS,GAAG5F,WAAW,CAAC8H,WAAW,IAAI,CAAC;IACxE;EACF;EAEA,OAAOtK,UAAU;AACnB,CAAC;AAEM,IAAMS,YAAY,GAAG,SAAfA,YAAYA,CAAIlE,IAAI,EAAK;EACpC,IAAIA,IAAI,CAACtB,OAAO,IAAI,CAACsB,IAAI,CAACvB,QAAQ,EAAE;IAClC,OAAO,CAAC;EACV;EACA,IAAIuB,IAAI,CAACxC,aAAa,EAAE;IACtB,OAAOwC,IAAI,CAACmE,UAAU;EACxB;EACA,OAAOnE,IAAI,CAAC/C,YAAY,IAAI+C,IAAI,CAACtD,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;AACtD,CAAC;AAEM,IAAM2H,aAAa,GAAG,SAAhBA,aAAaA,CAAIrE,IAAI,EAAK;EACrC,IAAIA,IAAI,CAACtB,OAAO,IAAI,CAACsB,IAAI,CAACvB,QAAQ,EAAE;IAClC,OAAO,CAAC;EACV;EACA,OAAOuB,IAAI,CAACmE,UAAU;AACxB,CAAC;AAEM,IAAM2O,cAAc,GAAG,SAAjBA,cAAcA,CAAI9S,IAAI;EAAA,OACjCA,IAAI,CAACmE,UAAU,KAAK,CAAC,GACjB,CAAC,GACDD,YAAY,CAAClE,IAAI,CAAC,GAAGA,IAAI,CAACmE,UAAU,GAAGE,aAAa,CAACrE,IAAI,CAAC;AAAA;AACzD,IAAM0P,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAI1P,IAAI,EAAK;EACxC,IAAIA,IAAI,CAACiG,WAAW,GAAGjG,IAAI,CAACV,YAAY,EAAE;IACxC,IAAIU,IAAI,CAACiG,WAAW,GAAGjG,IAAI,CAACV,YAAY,GAAGyU,aAAa,CAAC/T,IAAI,CAAC,EAAE;MAC9D,OAAO,MAAM;IACf;IACA,OAAO,OAAO;EAChB,CAAC,MAAM;IACL,IAAIA,IAAI,CAACiG,WAAW,GAAGjG,IAAI,CAACV,YAAY,GAAG0U,YAAY,CAAChU,IAAI,CAAC,EAAE;MAC7D,OAAO,OAAO;IAChB;IACA,OAAO,MAAM;EACf;AACF,CAAC;AAEM,IAAM+T,aAAa,GAAG,SAAhBA,aAAaA,CAAAtZ,IAAA,EAKpB;EAAA,IAJJwC,YAAY,GAAAxC,IAAA,CAAZwC,YAAY;IACZP,UAAU,GAAAjC,IAAA,CAAViC,UAAU;IACVgK,GAAG,GAAAjM,IAAA,CAAHiM,GAAG;IACHiD,aAAa,GAAAlP,IAAA,CAAbkP,aAAa;EAEb;EACA,IAAIjN,UAAU,EAAE;IACd,IAAIuX,KAAK,GAAG,CAAChX,YAAY,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;IACtC,IAAI4Q,QAAQ,CAAClE,aAAa,CAAC,GAAG,CAAC,EAAEsK,KAAK,IAAI,CAAC;IAC3C,IAAIvN,GAAG,IAAIzJ,YAAY,GAAG,CAAC,KAAK,CAAC,EAAEgX,KAAK,IAAI,CAAC;IAC7C,OAAOA,KAAK;EACd;EACA,IAAIvN,GAAG,EAAE;IACP,OAAO,CAAC;EACV;EACA,OAAOzJ,YAAY,GAAG,CAAC;AACzB,CAAC;AAEM,IAAM+W,YAAY,GAAG,SAAfA,YAAYA,CAAAE,KAAA,EAKnB;EAAA,IAJJjX,YAAY,GAAAiX,KAAA,CAAZjX,YAAY;IACZP,UAAU,GAAAwX,KAAA,CAAVxX,UAAU;IACVgK,GAAG,GAAAwN,KAAA,CAAHxN,GAAG;IACHiD,aAAa,GAAAuK,KAAA,CAAbvK,aAAa;EAEb;EACA,IAAIjN,UAAU,EAAE;IACd,IAAIiH,IAAI,GAAG,CAAC1G,YAAY,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;IACrC,IAAI4Q,QAAQ,CAAClE,aAAa,CAAC,GAAG,CAAC,EAAEhG,IAAI,IAAI,CAAC;IAC1C,IAAI,CAAC+C,GAAG,IAAIzJ,YAAY,GAAG,CAAC,KAAK,CAAC,EAAE0G,IAAI,IAAI,CAAC;IAC7C,OAAOA,IAAI;EACb;EACA,IAAI+C,GAAG,EAAE;IACP,OAAOzJ,YAAY,GAAG,CAAC;EACzB;EACA,OAAO,CAAC;AACV,CAAC;AAEM,IAAMrB,SAAS,GAAG,SAAZA,SAASA,CAAA;EAAA,OACpB,CAAC,EACC,OAAOtB,MAAM,KAAK,WAAW,IAC7BA,MAAM,CAAC2G,QAAQ,IACf3G,MAAM,CAAC2G,QAAQ,CAAC1C,aAAa,CAC9B;AAAA;AAEI,IAAM4V,aAAa,GAAG/I,MAAM,CAACC,IAAI,CAAC5O,sDAAY,CAAC;AAE/C,SAASqC,cAAcA,CAAC1C,QAAQ,EAAE;EACvC,OAAO+X,aAAa,CAACxB,MAAM,CAAC,UAACyB,GAAG,EAAEC,WAAW,EAAK;IAChD,IAAIjY,QAAQ,CAACkP,cAAc,CAAC+I,WAAW,CAAC,EAAE;MACxCD,GAAG,CAACC,WAAW,CAAC,GAAGjY,QAAQ,CAACiY,WAAW,CAAC;IAC1C;IACA,OAAOD,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR,C;;;;;;;ACr2BA;AAAA;AAAA;AAA0B;AAE1B,IAAI3X,YAAY,GAAG;EACjBgK,aAAa,EAAE,IAAI;EACnBtH,cAAc,EAAE,KAAK;EACrBoG,WAAW,EAAE,IAAI;EACjB+O,UAAU,EAAE,SAAAA,WAAC5L,IAAI;IAAA,oBAAKvL,4CAAA,CAAAoB,aAAA;MAAIL,KAAK,EAAE;QAAEI,OAAO,EAAE;MAAQ;IAAE,GAAEoK,IAAS,CAAC;EAAA;EAClEU,MAAM,EAAE,IAAI;EACZ/I,QAAQ,EAAE,KAAK;EACfwC,aAAa,EAAE,IAAI;EACnByC,YAAY,EAAE,IAAI;EAClB5I,UAAU,EAAE,KAAK;EACjBiN,aAAa,EAAE,MAAM;EACrBnL,SAAS,EAAE,EAAE;EACbiV,OAAO,EAAE,MAAM;EACfc,YAAY,EAAE,SAAAA,aAAC1W,CAAC;IAAA,oBAAKV,4CAAA,CAAAoB,aAAA,iBAASV,CAAC,GAAG,CAAU,CAAC;EAAA;EAC7C6K,IAAI,EAAE,KAAK;EACX8L,SAAS,EAAE,YAAY;EACvBtN,SAAS,EAAE,IAAI;EACfuN,MAAM,EAAE,QAAQ;EAChBnE,YAAY,EAAE,IAAI;EAClBtT,IAAI,EAAE,KAAK;EACXkJ,aAAa,EAAE,KAAK;EACpBzH,QAAQ,EAAE,IAAI;EACdsM,YAAY,EAAE,CAAC;EACfrL,QAAQ,EAAE,IAAI;EACdwJ,SAAS,EAAE,IAAI;EACfqH,MAAM,EAAE,IAAI;EACZ9Q,MAAM,EAAE,IAAI;EACZyF,eAAe,EAAE,IAAI;EACrB3C,QAAQ,EAAE,IAAI;EACdqG,gBAAgB,EAAE,KAAK;EACvBrH,YAAY,EAAE,KAAK;EACnB4G,YAAY,EAAE,IAAI;EAClBc,SAAS,EAAE,IAAI;EACflO,UAAU,EAAE,IAAI;EAChB0C,IAAI,EAAE,CAAC;EACPiJ,GAAG,EAAE,KAAK;EACVvN,KAAK,EAAE,KAAK;EACZuE,YAAY,EAAE,CAAC;EACff,cAAc,EAAE,CAAC;EACjBM,YAAY,EAAE,CAAC;EACf8D,KAAK,EAAE,GAAG;EACVkG,KAAK,EAAE,IAAI;EACXuJ,UAAU,EAAE,IAAI;EAChBH,YAAY,EAAE,KAAK;EACnBxG,SAAS,EAAE,IAAI;EACfqH,cAAc,EAAE,CAAC;EACjBxL,MAAM,EAAE,IAAI;EACZyN,YAAY,EAAE,IAAI;EAClB3V,aAAa,EAAE,KAAK;EACpBwK,QAAQ,EAAE,KAAK;EACfpC,cAAc,EAAE,IAAI;EACpBP,QAAQ,EAAE;AACZ,CAAC;AAEc5I,2EAAY,E;;;;;;;ACxD3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAa;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAAA1E,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,GAAAE,4EAAA,CAAAF,CAAA,GAAAG,uFAAA,CAAAJ,CAAA,EAAAK,sFAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAN,CAAA,EAAAC,CAAA,QAAAC,4EAAA,CAAAH,CAAA,EAAAQ,WAAA,IAAAP,CAAA,CAAAQ,KAAA,CAAAT,CAAA,EAAAE,CAAA;AAEa;AACU;AAKF;;AAElC;AACA,IAAMwc,eAAe,GAAG,SAAlBA,eAAeA,CAAI1U,IAAI,EAAK;EAChC,IAAI2U,WAAW,EAAEC,WAAW,EAAEC,WAAW;EACzC,IAAI/C,YAAY,EAAEvW,KAAK;EAEvB,IAAIyE,IAAI,CAAC0G,GAAG,EAAE;IACZnL,KAAK,GAAGyE,IAAI,CAACmE,UAAU,GAAG,CAAC,GAAGnE,IAAI,CAACzE,KAAK;EAC1C,CAAC,MAAM;IACLA,KAAK,GAAGyE,IAAI,CAACzE,KAAK;EACpB;EACAsZ,WAAW,GAAGtZ,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAIyE,IAAI,CAACmE,UAAU;EACnD,IAAInE,IAAI,CAACtD,UAAU,EAAE;IACnBoV,YAAY,GAAGlF,IAAI,CAACgB,KAAK,CAAC5N,IAAI,CAAC/C,YAAY,GAAG,CAAC,CAAC;IAChD2X,WAAW,GAAG,CAACrZ,KAAK,GAAGyE,IAAI,CAACV,YAAY,IAAIU,IAAI,CAACmE,UAAU,KAAK,CAAC;IACjE,IACE5I,KAAK,GAAGyE,IAAI,CAACV,YAAY,GAAGwS,YAAY,GAAG,CAAC,IAC5CvW,KAAK,IAAIyE,IAAI,CAACV,YAAY,GAAGwS,YAAY,EACzC;MACA6C,WAAW,GAAG,IAAI;IACpB;EACF,CAAC,MAAM;IACLA,WAAW,GACT3U,IAAI,CAACV,YAAY,IAAI/D,KAAK,IAC1BA,KAAK,GAAGyE,IAAI,CAACV,YAAY,GAAGU,IAAI,CAAC/C,YAAY;EACjD;EAEA,IAAI6X,YAAY;EAChB,IAAI9U,IAAI,CAACiG,WAAW,GAAG,CAAC,EAAE;IACxB6O,YAAY,GAAG9U,IAAI,CAACiG,WAAW,GAAGjG,IAAI,CAACmE,UAAU;EACnD,CAAC,MAAM,IAAInE,IAAI,CAACiG,WAAW,IAAIjG,IAAI,CAACmE,UAAU,EAAE;IAC9C2Q,YAAY,GAAG9U,IAAI,CAACiG,WAAW,GAAGjG,IAAI,CAACmE,UAAU;EACnD,CAAC,MAAM;IACL2Q,YAAY,GAAG9U,IAAI,CAACiG,WAAW;EACjC;EACA,IAAI8O,YAAY,GAAGxZ,KAAK,KAAKuZ,YAAY;EACzC,OAAO;IACL,aAAa,EAAE,IAAI;IACnB,cAAc,EAAEH,WAAW;IAC3B,cAAc,EAAEC,WAAW;IAC3B,cAAc,EAAEC,WAAW;IAC3B,eAAe,EAAEE,YAAY,CAAC;EAChC,CAAC;AACH,CAAC;AAED,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAIhV,IAAI,EAAK;EAC9B,IAAI9B,KAAK,GAAG,CAAC,CAAC;EAEd,IAAI8B,IAAI,CAACxC,aAAa,KAAKjE,SAAS,IAAIyG,IAAI,CAACxC,aAAa,KAAK,KAAK,EAAE;IACpEU,KAAK,CAACC,KAAK,GAAG6B,IAAI,CAACuE,UAAU;EAC/B;EAEA,IAAIvE,IAAI,CAAChD,IAAI,EAAE;IACbkB,KAAK,CAAC+W,QAAQ,GAAG,UAAU;IAC3B,IAAIjV,IAAI,CAACgI,QAAQ,IAAIhI,IAAI,CAAC+L,WAAW,EAAE;MACrC7N,KAAK,CAACgX,GAAG,GAAG,CAAClV,IAAI,CAACzE,KAAK,GAAGsS,QAAQ,CAAC7N,IAAI,CAAC+L,WAAW,CAAC;IACtD,CAAC,MAAM;MACL7N,KAAK,CAACyF,IAAI,GAAG,CAAC3D,IAAI,CAACzE,KAAK,GAAGsS,QAAQ,CAAC7N,IAAI,CAACuE,UAAU,CAAC;IACtD;IACArG,KAAK,CAAC8U,OAAO,GAAGhT,IAAI,CAACV,YAAY,KAAKU,IAAI,CAACzE,KAAK,GAAG,CAAC,GAAG,CAAC;IACxD2C,KAAK,CAACiX,MAAM,GAAGnV,IAAI,CAACV,YAAY,KAAKU,IAAI,CAACzE,KAAK,GAAG,GAAG,GAAG,GAAG;IAC3D,IAAIyE,IAAI,CAAC0F,MAAM,EAAE;MACfxH,KAAK,CAAC+U,UAAU,GACd,UAAU,GACVjT,IAAI,CAACe,KAAK,GACV,KAAK,GACLf,IAAI,CAACyT,OAAO,GACZ,IAAI,GACJ,aAAa,GACbzT,IAAI,CAACe,KAAK,GACV,KAAK,GACLf,IAAI,CAACyT,OAAO;IAChB;EACF;EAEA,OAAOvV,KAAK;AACd,CAAC;AAED,IAAMkX,MAAM,GAAG,SAATA,MAAMA,CAAI9X,KAAK,EAAE+X,WAAW;EAAA,OAAK/X,KAAK,CAACtD,GAAG,GAAG,GAAG,GAAGqb,WAAW;AAAA;AAEpE,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAItV,IAAI,EAAK;EAC7B,IAAIhG,GAAG;EACP,IAAIiY,MAAM,GAAG,EAAE;EACf,IAAIsD,cAAc,GAAG,EAAE;EACvB,IAAIC,eAAe,GAAG,EAAE;EACxB,IAAIlR,aAAa,GAAGnH,4CAAK,CAACC,QAAQ,CAACsF,KAAK,CAAC1C,IAAI,CAAC9C,QAAQ,CAAC;EACvD,IAAIkQ,UAAU,GAAGC,+EAAc,CAACrN,IAAI,CAAC;EACrC,IAAIsN,QAAQ,GAAGC,6EAAY,CAACvN,IAAI,CAAC;EAEjC7C,4CAAK,CAACC,QAAQ,CAAC9B,OAAO,CAAC0E,IAAI,CAAC9C,QAAQ,EAAE,UAACkC,IAAI,EAAE7D,KAAK,EAAK;IACrD,IAAI+B,KAAK;IACT,IAAImY,mBAAmB,GAAG;MACxB7S,OAAO,EAAE,UAAU;MACnBrH,KAAK,EAAEA,KAAK;MACZoB,cAAc,EAAEqD,IAAI,CAACrD,cAAc;MACnC2C,YAAY,EAAEU,IAAI,CAACV;IACrB,CAAC;;IAED;IACA,IACE,CAACU,IAAI,CAACN,QAAQ,IACbM,IAAI,CAACN,QAAQ,IAAIM,IAAI,CAACF,cAAc,CAACqF,OAAO,CAAC5J,KAAK,CAAC,IAAI,CAAE,EAC1D;MACA+B,KAAK,GAAG8B,IAAI;IACd,CAAC,MAAM;MACL9B,KAAK,gBAAGH,4CAAA,CAAAoB,aAAA,YAAM,CAAC;IACjB;IACA,IAAImX,UAAU,GAAGV,aAAa,CAAAxY,2EAAA,CAAAA,2EAAA,KAAMwD,IAAI;MAAEzE,KAAK,EAALA;IAAK,EAAE,CAAC;IAClD,IAAIoa,UAAU,GAAGrY,KAAK,CAAC3E,KAAK,CAAC6F,SAAS,IAAI,EAAE;IAC5C,IAAIoX,YAAY,GAAGlB,eAAe,CAAAlY,2EAAA,CAAAA,2EAAA,KAAMwD,IAAI;MAAEzE,KAAK,EAALA;IAAK,EAAE,CAAC;IACtD;IACA0W,MAAM,CAACrX,IAAI,eACTuC,4CAAK,CAACiB,YAAY,CAACd,KAAK,EAAE;MACxBtD,GAAG,EAAE,UAAU,GAAGob,MAAM,CAAC9X,KAAK,EAAE/B,KAAK,CAAC;MACtC,YAAY,EAAEA,KAAK;MACnBiD,SAAS,EAAEuJ,kDAAU,CAAC6N,YAAY,EAAED,UAAU,CAAC;MAC/CtX,QAAQ,EAAE,IAAI;MACd,aAAa,EAAE,CAACuX,YAAY,CAAC,cAAc,CAAC;MAC5C1X,KAAK,EAAA1B,2EAAA,CAAAA,2EAAA;QAAIqZ,OAAO,EAAE;MAAM,GAAMvY,KAAK,CAAC3E,KAAK,CAACuF,KAAK,IAAI,CAAC,CAAC,GAAMwX,UAAU,CAAE;MACvE3L,OAAO,EAAE,SAAAA,QAAC7R,CAAC,EAAK;QACdoF,KAAK,CAAC3E,KAAK,IAAI2E,KAAK,CAAC3E,KAAK,CAACoR,OAAO,IAAIzM,KAAK,CAAC3E,KAAK,CAACoR,OAAO,CAAC7R,CAAC,CAAC;QAC5D,IAAI8H,IAAI,CAACkG,aAAa,EAAE;UACtBlG,IAAI,CAACkG,aAAa,CAACuP,mBAAmB,CAAC;QACzC;MACF;IACF,CAAC,CACH,CAAC;;IAED;IACA,IACEzV,IAAI,CAACvB,QAAQ,IACb6F,aAAa,GAAG,CAAC,IACjBtE,IAAI,CAAChD,IAAI,KAAK,KAAK,IACnB,CAACgD,IAAI,CAACtB,OAAO,EACb;MACA,IAAIoX,UAAU,GAAGxR,aAAa,GAAG/I,KAAK;MACtC,IAAIua,UAAU,IAAI5R,6EAAY,CAAClE,IAAI,CAAC,EAAE;QACpChG,GAAG,GAAG,CAAC8b,UAAU;QACjB,IAAI9b,GAAG,IAAIoT,UAAU,EAAE;UACrB9P,KAAK,GAAG8B,IAAI;QACd;QACAwW,YAAY,GAAGlB,eAAe,CAAAlY,2EAAA,CAAAA,2EAAA,KAAMwD,IAAI;UAAEzE,KAAK,EAAEvB;QAAG,EAAE,CAAC;QACvDub,cAAc,CAAC3a,IAAI,eACjBuC,4CAAK,CAACiB,YAAY,CAACd,KAAK,EAAE;UACxBtD,GAAG,EAAE,WAAW,GAAGob,MAAM,CAAC9X,KAAK,EAAEtD,GAAG,CAAC;UACrC,YAAY,EAAEA,GAAG;UACjBqE,QAAQ,EAAE,IAAI;UACdG,SAAS,EAAEuJ,kDAAU,CAAC6N,YAAY,EAAED,UAAU,CAAC;UAC/C,aAAa,EAAE,CAACC,YAAY,CAAC,cAAc,CAAC;UAC5C1X,KAAK,EAAA1B,2EAAA,CAAAA,2EAAA,KAAQc,KAAK,CAAC3E,KAAK,CAACuF,KAAK,IAAI,CAAC,CAAC,GAAMwX,UAAU,CAAE;UACtD3L,OAAO,EAAE,SAAAA,QAAC7R,CAAC,EAAK;YACdoF,KAAK,CAAC3E,KAAK,IAAI2E,KAAK,CAAC3E,KAAK,CAACoR,OAAO,IAAIzM,KAAK,CAAC3E,KAAK,CAACoR,OAAO,CAAC7R,CAAC,CAAC;YAC5D,IAAI8H,IAAI,CAACkG,aAAa,EAAE;cACtBlG,IAAI,CAACkG,aAAa,CAACuP,mBAAmB,CAAC;YACzC;UACF;QACF,CAAC,CACH,CAAC;MACH;MAEAzb,GAAG,GAAGsK,aAAa,GAAG/I,KAAK;MAC3B,IAAIvB,GAAG,GAAGsT,QAAQ,EAAE;QAClBhQ,KAAK,GAAG8B,IAAI;MACd;MACAwW,YAAY,GAAGlB,eAAe,CAAAlY,2EAAA,CAAAA,2EAAA,KAAMwD,IAAI;QAAEzE,KAAK,EAAEvB;MAAG,EAAE,CAAC;MACvDwb,eAAe,CAAC5a,IAAI,eAClBuC,4CAAK,CAACiB,YAAY,CAACd,KAAK,EAAE;QACxBtD,GAAG,EAAE,YAAY,GAAGob,MAAM,CAAC9X,KAAK,EAAEtD,GAAG,CAAC;QACtC,YAAY,EAAEA,GAAG;QACjBqE,QAAQ,EAAE,IAAI;QACdG,SAAS,EAAEuJ,kDAAU,CAAC6N,YAAY,EAAED,UAAU,CAAC;QAC/C,aAAa,EAAE,CAACC,YAAY,CAAC,cAAc,CAAC;QAC5C1X,KAAK,EAAA1B,2EAAA,CAAAA,2EAAA,KAAQc,KAAK,CAAC3E,KAAK,CAACuF,KAAK,IAAI,CAAC,CAAC,GAAMwX,UAAU,CAAE;QACtD3L,OAAO,EAAE,SAAAA,QAAC7R,CAAC,EAAK;UACdoF,KAAK,CAAC3E,KAAK,IAAI2E,KAAK,CAAC3E,KAAK,CAACoR,OAAO,IAAIzM,KAAK,CAAC3E,KAAK,CAACoR,OAAO,CAAC7R,CAAC,CAAC;UAC5D,IAAI8H,IAAI,CAACkG,aAAa,EAAE;YACtBlG,IAAI,CAACkG,aAAa,CAACuP,mBAAmB,CAAC;UACzC;QACF;MACF,CAAC,CACH,CAAC;IACH;EACF,CAAC,CAAC;EAEF,IAAIzV,IAAI,CAAC0G,GAAG,EAAE;IACZ,OAAO6O,cAAc,CAACxY,MAAM,CAACkV,MAAM,EAAEuD,eAAe,CAAC,CAACO,OAAO,CAAC,CAAC;EACjE,CAAC,MAAM;IACL,OAAOR,cAAc,CAACxY,MAAM,CAACkV,MAAM,EAAEuD,eAAe,CAAC;EACvD;AACF,CAAC;AAEM,IAAM5K,KAAK,0BAAAoL,oBAAA;EAAA,SAAApL,MAAA;IAAA,IAAAhS,KAAA;IAAAC,4EAAA,OAAA+R,KAAA;IAAA,SAAAqL,IAAA,GAAA5c,SAAA,CAAAC,MAAA,EAAA4c,IAAA,OAAA/U,KAAA,CAAA8U,IAAA,GAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;MAAAD,IAAA,CAAAC,IAAA,IAAA9c,SAAA,CAAA8c,IAAA;IAAA;IAAAvd,KAAA,GAAAb,UAAA,OAAA6S,KAAA,KAAA7N,MAAA,CAAAmZ,IAAA;IAAApd,4EAAA,CAAAF,KAAA,UACT,IAAI;IAAAE,4EAAA,CAAAF,KAAA,eAEC,UAACG,GAAG,EAAK;MACnBH,KAAA,CAAKwK,IAAI,GAAGrK,GAAG;IACjB,CAAC;IAAA,OAAAH,KAAA;EAAA;EAAAkB,sEAAA,CAAA8Q,KAAA,EAAAoL,oBAAA;EAAA,OAAAjc,yEAAA,CAAA6Q,KAAA;IAAA5Q,GAAA;IAAAC,KAAA,EAED,SAAAiC,OAAA,EAAS;MACP,IAAM+V,MAAM,GAAGqD,YAAY,CAAC,IAAI,CAAC3c,KAAK,CAAC;MACvC,IAAAyM,WAAA,GAAoD,IAAI,CAACzM,KAAK;QAAtDyP,YAAY,GAAAhD,WAAA,CAAZgD,YAAY;QAAEI,WAAW,GAAApD,WAAA,CAAXoD,WAAW;QAAEF,YAAY,GAAAlD,WAAA,CAAZkD,YAAY;MAC/C,IAAM8N,WAAW,GAAG;QAAEhO,YAAY,EAAZA,YAAY;QAAEI,WAAW,EAAXA,WAAW;QAAEF,YAAY,EAAZA;MAAa,CAAC;MAC/D,oBACEnL,4CAAA,CAAAoB,aAAA,QAAAK,qEAAA;QACE7F,GAAG,EAAE,IAAI,CAACsd,SAAU;QACpB7X,SAAS,EAAC,aAAa;QACvBN,KAAK,EAAE,IAAI,CAACvF,KAAK,CAACiL;MAAW,GACzBwS,WAAW,GAEdnE,MACE,CAAC;IAEV;EAAC;AAAA,EArBwB9U,4CAAK,CAACmZ,aAAa,E;;;;;;;ACxM9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAa;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAAAve,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,GAAAE,4EAAA,CAAAF,CAAA,GAAAG,uFAAA,CAAAJ,CAAA,EAAAK,sFAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAN,CAAA,EAAAC,CAAA,QAAAC,4EAAA,CAAAH,CAAA,EAAAQ,WAAA,IAAAP,CAAA,CAAAQ,KAAA,CAAAT,CAAA,EAAAE,CAAA;AAEa;AACU;AACa;AAEjD,IAAMqe,WAAW,GAAG,SAAdA,WAAWA,CAAGvW,IAAI,EAAI;EAC1B,IAAI0I,IAAI;EAER,IAAI1I,IAAI,CAACvB,QAAQ,EAAE;IACjBiK,IAAI,GAAGkE,IAAI,CAACiC,IAAI,CAAC7O,IAAI,CAACmE,UAAU,GAAGnE,IAAI,CAACrD,cAAc,CAAC;EACzD,CAAC,MAAM;IACL+L,IAAI,GACFkE,IAAI,CAACiC,IAAI,CAAC,CAAC7O,IAAI,CAACmE,UAAU,GAAGnE,IAAI,CAAC/C,YAAY,IAAI+C,IAAI,CAACrD,cAAc,CAAC,GACtE,CAAC;EACL;EAEA,OAAO+L,IAAI;AACb,CAAC;AAEM,IAAMM,IAAI,0BAAAgN,oBAAA;EAAA,SAAAhN,KAAA;IAAAnQ,4EAAA,OAAAmQ,IAAA;IAAA,OAAAjR,UAAA,OAAAiR,IAAA,EAAA3P,SAAA;EAAA;EAAAS,sEAAA,CAAAkP,IAAA,EAAAgN,oBAAA;EAAA,OAAAjc,yEAAA,CAAAiP,IAAA;IAAAhP,GAAA;IAAAC,KAAA,EACf,SAAA4O,aAAa7C,OAAO,EAAE9N,CAAC,EAAE;MACvB;MACA;MACAA,CAAC,CAACoO,cAAc,CAAC,CAAC;MAClB,IAAI,CAAC3N,KAAK,CAACkQ,YAAY,CAAC7C,OAAO,CAAC;IAClC;EAAC;IAAAhM,GAAA;IAAAC,KAAA,EACD,SAAAiC,OAAA,EAAS;MACP,IAAAkJ,WAAA,GASI,IAAI,CAACzM,KAAK;QARZyP,YAAY,GAAAhD,WAAA,CAAZgD,YAAY;QACZI,WAAW,GAAApD,WAAA,CAAXoD,WAAW;QACXF,YAAY,GAAAlD,WAAA,CAAZkD,YAAY;QACZ7J,QAAQ,GAAA2G,WAAA,CAAR3G,QAAQ;QACR9B,cAAc,GAAAyI,WAAA,CAAdzI,cAAc;QACdM,YAAY,GAAAmI,WAAA,CAAZnI,YAAY;QACZkH,UAAU,GAAAiB,WAAA,CAAVjB,UAAU;QACV7E,YAAY,GAAA8F,WAAA,CAAZ9F,YAAY;MAEd,IAAIyR,QAAQ,GAAGwF,WAAW,CAAC;QACzBpS,UAAU,EAAVA,UAAU;QACVxH,cAAc,EAAdA,cAAc;QACdM,YAAY,EAAZA,YAAY;QACZwB,QAAQ,EAARA;MACF,CAAC,CAAC;MAEF,IAAM2X,WAAW,GAAG;QAAEhO,YAAY,EAAZA,YAAY;QAAEI,WAAW,EAAXA,WAAW;QAAEF,YAAY,EAAZA;MAAa,CAAC;MAC/D,IAAII,IAAI,GAAG,EAAE;MACb,KAAK,IAAI7K,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkT,QAAQ,EAAElT,CAAC,EAAE,EAAE;QACjC,IAAI2Y,WAAW,GAAG,CAAC3Y,CAAC,GAAG,CAAC,IAAIlB,cAAc,GAAG,CAAC;QAC9C,IAAI8Z,UAAU,GAAGhY,QAAQ,GACrB+X,WAAW,GACXhK,qEAAK,CAACgK,WAAW,EAAE,CAAC,EAAErS,UAAU,GAAG,CAAC,CAAC;QACzC,IAAIuS,UAAU,GAAGD,UAAU,IAAI9Z,cAAc,GAAG,CAAC,CAAC;QAClD,IAAIga,SAAS,GAAGlY,QAAQ,GACpBiY,UAAU,GACVlK,qEAAK,CAACkK,UAAU,EAAE,CAAC,EAAEvS,UAAU,GAAG,CAAC,CAAC;QAExC,IAAI3F,SAAS,GAAGuJ,iDAAU,CAAC;UACzB,cAAc,EAAEtJ,QAAQ,GACpBa,YAAY,IAAIqX,SAAS,IAAIrX,YAAY,IAAImX,UAAU,GACvDnX,YAAY,KAAKqX;QACvB,CAAC,CAAC;QAEF,IAAIC,UAAU,GAAG;UACfhU,OAAO,EAAE,MAAM;UACfrH,KAAK,EAAEsC,CAAC;UACRlB,cAAc,EAAdA,cAAc;UACd2C,YAAY,EAAZA;QACF,CAAC;QAED,IAAIyK,OAAO,GAAG,IAAI,CAAClB,YAAY,CAACgO,IAAI,CAAC,IAAI,EAAED,UAAU,CAAC;QACtDlO,IAAI,GAAGA,IAAI,CAAC3L,MAAM,eAChBI,4CAAA,CAAAoB,aAAA;UAAIvE,GAAG,EAAE6D,CAAE;UAACW,SAAS,EAAEA;QAAU,gBAC9BrB,4CAAK,CAACiB,YAAY,CAAC,IAAI,CAACzF,KAAK,CAAC4b,YAAY,CAAC1W,CAAC,CAAC,EAAE;UAAEkM,OAAO,EAAPA;QAAQ,CAAC,CACzD,CACN,CAAC;MACH;MAEA,oBAAO5M,4CAAK,CAACiB,YAAY,CAAC,IAAI,CAACzF,KAAK,CAAC2b,UAAU,CAAC5L,IAAI,CAAC,EAAAlM,2EAAA;QACnDgC,SAAS,EAAE,IAAI,CAAC7F,KAAK,CAAC6b;MAAS,GAC5B4B,WAAW,CACf,CAAC;IACJ;EAAC;AAAA,EA9DuBjZ,4CAAK,CAACmZ,aAAa,E;;;;;;;ACpB7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAa;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAAAve,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,GAAAE,4EAAA,CAAAF,CAAA,GAAAG,uFAAA,CAAAJ,CAAA,EAAAK,sFAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAN,CAAA,EAAAC,CAAA,QAAAC,4EAAA,CAAAH,CAAA,EAAAQ,WAAA,IAAAP,CAAA,CAAAQ,KAAA,CAAAT,CAAA,EAAAE,CAAA;AAEa;AACU;AACiB;AAE9C,IAAMmR,SAAS,0BAAA2M,oBAAA;EAAA,SAAA3M,UAAA;IAAAxQ,4EAAA,OAAAwQ,SAAA;IAAA,OAAAtR,UAAA,OAAAsR,SAAA,EAAAhQ,SAAA;EAAA;EAAAS,sEAAA,CAAAuP,SAAA,EAAA2M,oBAAA;EAAA,OAAAjc,yEAAA,CAAAsP,SAAA;IAAArP,GAAA;IAAAC,KAAA,EACpB,SAAA4O,aAAa7C,OAAO,EAAE9N,CAAC,EAAE;MACvB,IAAIA,CAAC,EAAE;QACLA,CAAC,CAACoO,cAAc,CAAC,CAAC;MACpB;MACA,IAAI,CAAC3N,KAAK,CAACkQ,YAAY,CAAC7C,OAAO,EAAE9N,CAAC,CAAC;IACrC;EAAC;IAAA8B,GAAA;IAAAC,KAAA,EACD,SAAAiC,OAAA,EAAS;MACP,IAAI4a,WAAW,GAAG;QAAE,aAAa,EAAE,IAAI;QAAE,YAAY,EAAE;MAAK,CAAC;MAC7D,IAAIC,WAAW,GAAG,IAAI,CAAClO,YAAY,CAACgO,IAAI,CAAC,IAAI,EAAE;QAAEjU,OAAO,EAAE;MAAW,CAAC,CAAC;MAEvE,IACE,CAAC,IAAI,CAACjK,KAAK,CAAC8F,QAAQ,KACnB,IAAI,CAAC9F,KAAK,CAAC2G,YAAY,KAAK,CAAC,IAC5B,IAAI,CAAC3G,KAAK,CAACwL,UAAU,IAAI,IAAI,CAACxL,KAAK,CAACsE,YAAY,CAAC,EACnD;QACA6Z,WAAW,CAAC,gBAAgB,CAAC,GAAG,IAAI;QACpCC,WAAW,GAAG,IAAI;MACpB;MAEA,IAAIC,cAAc,GAAG;QACnBhd,GAAG,EAAE,GAAG;QACR,WAAW,EAAE,MAAM;QACnBwE,SAAS,EAAEuJ,iDAAU,CAAC+O,WAAW,CAAC;QAClC5Y,KAAK,EAAE;UAAEI,OAAO,EAAE;QAAQ,CAAC;QAC3ByL,OAAO,EAAEgN;MACX,CAAC;MACD,IAAIE,WAAW,GAAG;QAChB3X,YAAY,EAAE,IAAI,CAAC3G,KAAK,CAAC2G,YAAY;QACrC6E,UAAU,EAAE,IAAI,CAACxL,KAAK,CAACwL;MACzB,CAAC;MACD,IAAI8E,SAAS;MAEb,IAAI,IAAI,CAACtQ,KAAK,CAACsQ,SAAS,EAAE;QACxBA,SAAS,gBAAG9L,4CAAK,CAACiB,YAAY,CAAC,IAAI,CAACzF,KAAK,CAACsQ,SAAS,EAAAzM,2EAAA,CAAAA,2EAAA,KAC9Cwa,cAAc,GACdC,WAAW,CACf,CAAC;MACJ,CAAC,MAAM;QACLhO,SAAS,gBACP9L,4CAAA,CAAAoB,aAAA,WAAAK,qEAAA;UAAQ5E,GAAG,EAAC,GAAG;UAAC+V,IAAI,EAAC;QAAQ,GAAKiH,cAAc,GAC7C,GAAG,EAAC,UAEC,CACT;MACH;MAEA,OAAO/N,SAAS;IAClB;EAAC;AAAA,EAhD4B9L,4CAAK,CAACmZ,aAAa;AAmD3C,IAAMhN,SAAS,0BAAA4N,qBAAA;EAAA,SAAA5N,UAAA;IAAAzQ,4EAAA,OAAAyQ,SAAA;IAAA,OAAAvR,UAAA,OAAAuR,SAAA,EAAAjQ,SAAA;EAAA;EAAAS,sEAAA,CAAAwP,SAAA,EAAA4N,qBAAA;EAAA,OAAAnd,yEAAA,CAAAuP,SAAA;IAAAtP,GAAA;IAAAC,KAAA,EACpB,SAAA4O,aAAa7C,OAAO,EAAE9N,CAAC,EAAE;MACvB,IAAIA,CAAC,EAAE;QACLA,CAAC,CAACoO,cAAc,CAAC,CAAC;MACpB;MACA,IAAI,CAAC3N,KAAK,CAACkQ,YAAY,CAAC7C,OAAO,EAAE9N,CAAC,CAAC;IACrC;EAAC;IAAA8B,GAAA;IAAAC,KAAA,EACD,SAAAiC,OAAA,EAAS;MACP,IAAIib,WAAW,GAAG;QAAE,aAAa,EAAE,IAAI;QAAE,YAAY,EAAE;MAAK,CAAC;MAC7D,IAAIC,WAAW,GAAG,IAAI,CAACvO,YAAY,CAACgO,IAAI,CAAC,IAAI,EAAE;QAAEjU,OAAO,EAAE;MAAO,CAAC,CAAC;MAEnE,IAAI,CAAC8E,0EAAS,CAAC,IAAI,CAAC/O,KAAK,CAAC,EAAE;QAC1Bwe,WAAW,CAAC,gBAAgB,CAAC,GAAG,IAAI;QACpCC,WAAW,GAAG,IAAI;MACpB;MAEA,IAAIC,cAAc,GAAG;QACnBrd,GAAG,EAAE,GAAG;QACR,WAAW,EAAE,MAAM;QACnBwE,SAAS,EAAEuJ,iDAAU,CAACoP,WAAW,CAAC;QAClCjZ,KAAK,EAAE;UAAEI,OAAO,EAAE;QAAQ,CAAC;QAC3ByL,OAAO,EAAEqN;MACX,CAAC;MACD,IAAIH,WAAW,GAAG;QAChB3X,YAAY,EAAE,IAAI,CAAC3G,KAAK,CAAC2G,YAAY;QACrC6E,UAAU,EAAE,IAAI,CAACxL,KAAK,CAACwL;MACzB,CAAC;MACD,IAAI+E,SAAS;MAEb,IAAI,IAAI,CAACvQ,KAAK,CAACuQ,SAAS,EAAE;QACxBA,SAAS,gBAAG/L,4CAAK,CAACiB,YAAY,CAAC,IAAI,CAACzF,KAAK,CAACuQ,SAAS,EAAA1M,2EAAA,CAAAA,2EAAA,KAC9C6a,cAAc,GACdJ,WAAW,CACf,CAAC;MACJ,CAAC,MAAM;QACL/N,SAAS,gBACP/L,4CAAA,CAAAoB,aAAA,WAAAK,qEAAA;UAAQ5E,GAAG,EAAC,GAAG;UAAC+V,IAAI,EAAC;QAAQ,GAAKsH,cAAc,GAC7C,GAAG,EAAC,MAEC,CACT;MACH;MAEA,OAAOnO,SAAS;IAClB;EAAC;AAAA,EA5C4B/L,4CAAK,CAACmZ,aAAa,E;;;;;;;ACzDlD;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,aAAa;AAC5B,eAAe,EAAE;AACjB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA,mBAAmB,EAAE;AACrB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,EAAE;AACrB,mBAAmB,EAAE;AACrB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,EAAE;AACrB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,EAAE;AACrB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,mBAAmB,SAAS;AAC5B,mBAAmB,EAAE;AACrB,qBAAqB;AACrB;AACA;AACA,iCAAiC,YAAY;AAC7C,mDAAmD,gBAAgB;AACnE;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,CAAC;;AAED;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA,aAAa,OAAO;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,gCAAgC,6BAA6B,EAAE,aAAa;AAC5G,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,SAAS;AACpB,WAAW,OAAO;AAClB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,kBAAkB;AACjC,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,kBAAkB;AACjC,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,QAAQ;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,qDAAqD,mCAAmC,EAAE;AAC1F;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,gBAAgB;AAC/B,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,aAAa,OAAO;AACpB;AACA;AACA,6CAA6C,gBAAgB;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,WAAW,OAAO;AAClB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,oBAAoB;AAC/B,WAAW,UAAU;AACrB,aAAa;AACb;AACA;AACA;AACA,oBAAoB,uBAAuB;AAC3C;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,WAAW,oBAAoB;AAC/B,aAAa,OAAO;AACpB;AACA;AACA;AACA;AACA,6CAA6C,yBAAyB;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,mBAAmB;AAC9B;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,YAAY;AACvB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,kCAAkC,iEAAiE;AACnG;AACA;AACA;AACA;AACA,8BAA8B;AAC9B,8CAA8C;AAC9C,CAAC;AACD;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,YAAY;AACvB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,aAAa;AACb;AACA;AACA,YAAY;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,YAAY;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,YAAY;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,2CAA2C;AAC7E;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA,eAAe,uBAAuB;AACtC;AACA,eAAe,yBAAyB;AACxC;AACA,eAAe,eAAe;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,uBAAuB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAEc,oEAAK,EAAC;;;;;;;;AC/5BrB;;AAEA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;;AAEA;AACA;AACA,4CAA4C;;AAE5C;;;;;;;ACnBA,mBAAmB,mBAAO,CAAC,EAA6B;;AAExD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA,yB;;;;;;AClDA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;;AAEA,8B", "file": "react-slick.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"react\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"react\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Slider\"] = factory(require(\"react\"));\n\telse\n\t\troot[\"Slider\"] = factory(root[\"React\"]);\n})(window, function(__WEBPACK_EXTERNAL_MODULE__16__) {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 0);\n", "import Slider from \"./slider\";\n\nexport default Slider;\n", "\"use strict\";\n\nimport React from \"react\";\nimport { InnerSlider } from \"./inner-slider\";\nimport json2mq from \"json2mq\";\nimport defaultProps from \"./default-props\";\nimport { canUseDOM, filterSettings } from \"./utils/innerSliderUtils\";\n\nexport default class Slider extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      breakpoint: null\n    };\n    this._responsiveMediaHandlers = [];\n  }\n\n  innerSliderRefHandler = (ref) => (this.innerSlider = ref);\n\n  media(query, handler) {\n    // javascript handler for  css media query\n    const mql = window.matchMedia(query);\n    const listener = ({ matches }) => {\n      if (matches) {\n        handler();\n      }\n    };\n    mql.addListener(listener);\n    listener(mql);\n    this._responsiveMediaHandlers.push({ mql, query, listener });\n  }\n\n  // handles responsive breakpoints\n  componentDidMount() {\n    // performance monitoring\n    //if (process.env.NODE_ENV !== 'production') {\n    //const { whyDidYouUpdate } = require('why-did-you-update')\n    //whyDidYouUpdate(React)\n    //}\n    if (this.props.responsive) {\n      let breakpoints = this.props.responsive.map(\n        (breakpt) => breakpt.breakpoint\n      );\n      // sort them in increasing order of their numerical value\n      breakpoints.sort((x, y) => x - y);\n\n      breakpoints.forEach((breakpoint, index) => {\n        // media query for each breakpoint\n        let bQuery;\n        if (index === 0) {\n          bQuery = json2mq({ minWidth: 0, maxWidth: breakpoint });\n        } else {\n          bQuery = json2mq({\n            minWidth: breakpoints[index - 1] + 1,\n            maxWidth: breakpoint\n          });\n        }\n        // when not using server side rendering\n        canUseDOM() &&\n          this.media(bQuery, () => {\n            this.setState({ breakpoint: breakpoint });\n          });\n      });\n\n      // Register media query for full screen. Need to support resize from small to large\n      // convert javascript object to media query string\n      let query = json2mq({ minWidth: breakpoints.slice(-1)[0] });\n\n      canUseDOM() &&\n        this.media(query, () => {\n          this.setState({ breakpoint: null });\n        });\n    }\n  }\n\n  componentWillUnmount() {\n    this._responsiveMediaHandlers.forEach(function (obj) {\n      obj.mql.removeListener(obj.listener);\n    });\n  }\n\n  slickPrev = () => this.innerSlider.slickPrev();\n\n  slickNext = () => this.innerSlider.slickNext();\n\n  slickGoTo = (slide, dontAnimate = false) =>\n    this.innerSlider.slickGoTo(slide, dontAnimate);\n\n  slickPause = () => this.innerSlider.pause(\"paused\");\n\n  slickPlay = () => this.innerSlider.autoPlay(\"play\");\n\n  render() {\n    var settings;\n    var newProps;\n    if (this.state.breakpoint) {\n      newProps = this.props.responsive.filter(\n        (resp) => resp.breakpoint === this.state.breakpoint\n      );\n      settings =\n        newProps[0].settings === \"unslick\"\n          ? \"unslick\"\n          : { ...defaultProps, ...this.props, ...newProps[0].settings };\n    } else {\n      settings = { ...defaultProps, ...this.props };\n    }\n\n    // force scrolling by one if centerMode is on\n    if (settings.centerMode) {\n      if (\n        settings.slidesToScroll > 1 &&\n        process.env.NODE_ENV !== \"production\"\n      ) {\n        console.warn(\n          `slidesToScroll should be equal to 1 in centerMode, you are using ${settings.slidesToScroll}`\n        );\n      }\n      settings.slidesToScroll = 1;\n    }\n    // force showing one slide and scrolling by one if the fade mode is on\n    if (settings.fade) {\n      if (settings.slidesToShow > 1 && process.env.NODE_ENV !== \"production\") {\n        console.warn(\n          `slidesToShow should be equal to 1 when fade is true, you're using ${settings.slidesToShow}`\n        );\n      }\n      if (\n        settings.slidesToScroll > 1 &&\n        process.env.NODE_ENV !== \"production\"\n      ) {\n        console.warn(\n          `slidesToScroll should be equal to 1 when fade is true, you're using ${settings.slidesToScroll}`\n        );\n      }\n      settings.slidesToShow = 1;\n      settings.slidesToScroll = 1;\n    }\n\n    // makes sure that children is an array, even when there is only 1 child\n    let children = React.Children.toArray(this.props.children);\n\n    // Children may contain false or null, so we should filter them\n    // children may also contain string filled with spaces (in certain cases where we use jsx strings)\n    children = children.filter((child) => {\n      if (typeof child === \"string\") {\n        return !!child.trim();\n      }\n      return !!child;\n    });\n\n    // rows and slidesPerRow logic is handled here\n    if (\n      settings.variableWidth &&\n      (settings.rows > 1 || settings.slidesPerRow > 1)\n    ) {\n      console.warn(\n        `variableWidth is not supported in case of rows > 1 or slidesPerRow > 1`\n      );\n      settings.variableWidth = false;\n    }\n    let newChildren = [];\n    let currentWidth = null;\n    for (\n      let i = 0;\n      i < children.length;\n      i += settings.rows * settings.slidesPerRow\n    ) {\n      let newSlide = [];\n      for (\n        let j = i;\n        j < i + settings.rows * settings.slidesPerRow;\n        j += settings.slidesPerRow\n      ) {\n        let row = [];\n        for (let k = j; k < j + settings.slidesPerRow; k += 1) {\n          if (settings.variableWidth && children[k].props.style) {\n            currentWidth = children[k].props.style.width;\n          }\n          if (k >= children.length) break;\n          row.push(\n            React.cloneElement(children[k], {\n              key: 100 * i + 10 * j + k,\n              tabIndex: -1,\n              style: {\n                width: `${100 / settings.slidesPerRow}%`,\n                display: \"inline-block\"\n              }\n            })\n          );\n        }\n        newSlide.push(<div key={10 * i + j}>{row}</div>);\n      }\n      if (settings.variableWidth) {\n        newChildren.push(\n          <div key={i} style={{ width: currentWidth }}>\n            {newSlide}\n          </div>\n        );\n      } else {\n        newChildren.push(<div key={i}>{newSlide}</div>);\n      }\n    }\n\n    if (settings === \"unslick\") {\n      const className = \"regular slider \" + (this.props.className || \"\");\n      return <div className={className}>{children}</div>;\n    } else if (\n      newChildren.length <= settings.slidesToShow &&\n      !settings.infinite\n    ) {\n      settings.unslick = true;\n    }\n    return (\n      <InnerSlider\n        style={this.props.style}\n        ref={this.innerSliderRefHandler}\n        {...filterSettings(settings)}\n      >\n        {newChildren}\n      </InnerSlider>\n    );\n  }\n}\n", "function _extends() {\n  module.exports = _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n  return _extends.apply(this, arguments);\n}\nmodule.exports = _extends, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var defineProperty = require(\"./defineProperty.js\");\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nmodule.exports = _objectSpread2, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var toPropertyKey = require(\"./toPropertyKey.js\");\nfunction _defineProperty(obj, key, value) {\n  key = toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nmodule.exports = _defineProperty, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nvar toPrimitive = require(\"./toPrimitive.js\");\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nmodule.exports = toPropertyKey, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return (module.exports = _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports), _typeof(o);\n}\nmodule.exports = _typeof, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nmodule.exports = toPrimitive, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nmodule.exports = _classCallCheck, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var toPropertyKey = require(\"./toPropertyKey.js\");\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nmodule.exports = _createClass, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nvar assertThisInitialized = require(\"./assertThisInitialized.js\");\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return assertThisInitialized(self);\n}\nmodule.exports = _possibleConstructorReturn, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nmodule.exports = _assertThisInitialized, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {}));\n  } catch (t) {}\n  return (module.exports = _isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports)();\n}\nmodule.exports = _isNativeReflectConstruct, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _getPrototypeOf(o) {\n  module.exports = _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n  return _getPrototypeOf(o);\n}\nmodule.exports = _getPrototypeOf, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var setPrototypeOf = require(\"./setPrototypeOf.js\");\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) setPrototypeOf(subClass, superClass);\n}\nmodule.exports = _inherits, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _setPrototypeOf(o, p) {\n  module.exports = _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n  return _setPrototypeOf(o, p);\n}\nmodule.exports = _setPrototypeOf, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "module.exports = __WEBPACK_EXTERNAL_MODULE__16__;", "\"use strict\";\n\nimport React from \"react\";\nimport initialState from \"./initial-state\";\nimport { debounce } from \"throttle-debounce\";\nimport classnames from \"classnames\";\nimport {\n  getOnDemandLazySlides,\n  extractObject,\n  initializedState,\n  getHeight,\n  canGoNext,\n  slideHandler,\n  changeSlide,\n  keyHandler,\n  swipeStart,\n  swipeMove,\n  swipeEnd,\n  getPreClones,\n  getPostClones,\n  getTrackLeft,\n  getTrackCSS\n} from \"./utils/innerSliderUtils\";\n\nimport { Track } from \"./track\";\nimport { Dots } from \"./dots\";\nimport { PrevArrow, NextArrow } from \"./arrows\";\nimport ResizeObserver from \"resize-observer-polyfill\";\n\nexport class InnerSlider extends React.Component {\n  constructor(props) {\n    super(props);\n    this.list = null;\n    this.track = null;\n    this.state = {\n      ...initialState,\n      currentSlide: this.props.initialSlide,\n      targetSlide: this.props.initialSlide ? this.props.initialSlide : 0,\n      slideCount: React.Children.count(this.props.children)\n    };\n    this.callbackTimers = [];\n    this.clickable = true;\n    this.debouncedResize = null;\n    const ssrState = this.ssrInit();\n    this.state = { ...this.state, ...ssrState };\n  }\n  listRefHandler = (ref) => (this.list = ref);\n  trackRefHandler = (ref) => (this.track = ref);\n  adaptHeight = () => {\n    if (this.props.adaptiveHeight && this.list) {\n      const elem = this.list.querySelector(\n        `[data-index=\"${this.state.currentSlide}\"]`\n      );\n      this.list.style.height = getHeight(elem) + \"px\";\n    }\n  };\n  componentDidMount = () => {\n    this.props.onInit && this.props.onInit();\n    if (this.props.lazyLoad) {\n      let slidesToLoad = getOnDemandLazySlides({\n        ...this.props,\n        ...this.state\n      });\n      if (slidesToLoad.length > 0) {\n        this.setState((prevState) => ({\n          lazyLoadedList: prevState.lazyLoadedList.concat(slidesToLoad)\n        }));\n        if (this.props.onLazyLoad) {\n          this.props.onLazyLoad(slidesToLoad);\n        }\n      }\n    }\n    let spec = { listRef: this.list, trackRef: this.track, ...this.props };\n    this.updateState(spec, true, () => {\n      this.adaptHeight();\n      this.props.autoplay && this.autoPlay(\"playing\");\n    });\n    if (this.props.lazyLoad === \"progressive\") {\n      this.lazyLoadTimer = setInterval(this.progressiveLazyLoad, 1000);\n    }\n    this.ro = new ResizeObserver(() => {\n      if (this.state.animating) {\n        this.onWindowResized(false); // don't set trackStyle hence don't break animation\n        this.callbackTimers.push(\n          setTimeout(() => this.onWindowResized(), this.props.speed)\n        );\n      } else {\n        this.onWindowResized();\n      }\n    });\n    this.ro.observe(this.list);\n    document.querySelectorAll &&\n      Array.prototype.forEach.call(\n        document.querySelectorAll(\".slick-slide\"),\n        (slide) => {\n          slide.onfocus = this.props.pauseOnFocus ? this.onSlideFocus : null;\n          slide.onblur = this.props.pauseOnFocus ? this.onSlideBlur : null;\n        }\n      );\n    if (window.addEventListener) {\n      window.addEventListener(\"resize\", this.onWindowResized);\n    } else {\n      window.attachEvent(\"onresize\", this.onWindowResized);\n    }\n  };\n  componentWillUnmount = () => {\n    if (this.animationEndCallback) {\n      clearTimeout(this.animationEndCallback);\n    }\n    if (this.lazyLoadTimer) {\n      clearInterval(this.lazyLoadTimer);\n    }\n    if (this.callbackTimers.length) {\n      this.callbackTimers.forEach((timer) => clearTimeout(timer));\n      this.callbackTimers = [];\n    }\n    if (window.addEventListener) {\n      window.removeEventListener(\"resize\", this.onWindowResized);\n    } else {\n      window.detachEvent(\"onresize\", this.onWindowResized);\n    }\n    if (this.autoplayTimer) {\n      clearInterval(this.autoplayTimer);\n    }\n    this.ro.disconnect();\n  };\n\n  didPropsChange(prevProps) {\n    let setTrackStyle = false;\n    for (let key of Object.keys(this.props)) {\n      // eslint-disable-next-line no-prototype-builtins\n      if (!prevProps.hasOwnProperty(key)) {\n        setTrackStyle = true;\n        break;\n      }\n      if (\n        typeof prevProps[key] === \"object\" ||\n        typeof prevProps[key] === \"function\" ||\n        isNaN(prevProps[key])\n      ) {\n        continue;\n      }\n      if (prevProps[key] !== this.props[key]) {\n        setTrackStyle = true;\n        break;\n      }\n    }\n    return (\n      setTrackStyle ||\n      React.Children.count(this.props.children) !==\n        React.Children.count(prevProps.children)\n    );\n  }\n\n  componentDidUpdate = (prevProps) => {\n    this.checkImagesLoad();\n    this.props.onReInit && this.props.onReInit();\n    if (this.props.lazyLoad) {\n      let slidesToLoad = getOnDemandLazySlides({\n        ...this.props,\n        ...this.state\n      });\n      if (slidesToLoad.length > 0) {\n        this.setState((prevState) => ({\n          lazyLoadedList: prevState.lazyLoadedList.concat(slidesToLoad)\n        }));\n        if (this.props.onLazyLoad) {\n          this.props.onLazyLoad(slidesToLoad);\n        }\n      }\n    }\n    // if (this.props.onLazyLoad) {\n    //   this.props.onLazyLoad([leftMostSlide])\n    // }\n    this.adaptHeight();\n    let spec = {\n      listRef: this.list,\n      trackRef: this.track,\n      ...this.props,\n      ...this.state\n    };\n    const setTrackStyle = this.didPropsChange(prevProps);\n    setTrackStyle &&\n      this.updateState(spec, setTrackStyle, () => {\n        if (\n          this.state.currentSlide >= React.Children.count(this.props.children)\n        ) {\n          this.changeSlide({\n            message: \"index\",\n            index:\n              React.Children.count(this.props.children) -\n              this.props.slidesToShow,\n            currentSlide: this.state.currentSlide\n          });\n        }\n        if (\n          prevProps.autoplay !== this.props.autoplay ||\n          prevProps.autoplaySpeed !== this.props.autoplaySpeed\n        ) {\n          if (!prevProps.autoplay && this.props.autoplay) {\n            this.autoPlay(\"playing\");\n          } else if (this.props.autoplay) {\n            this.autoPlay(\"update\");\n          } else {\n            this.pause(\"paused\");\n          }\n        }\n      });\n  };\n  onWindowResized = (setTrackStyle) => {\n    if (this.debouncedResize) this.debouncedResize.cancel();\n    this.debouncedResize = debounce(50, () => this.resizeWindow(setTrackStyle));\n    this.debouncedResize();\n  };\n  resizeWindow = (setTrackStyle = true) => {\n    const isTrackMounted = Boolean(this.track && this.track.node);\n    // prevent warning: setting state on unmounted component (server side rendering)\n    if (!isTrackMounted) return;\n    let spec = {\n      listRef: this.list,\n      trackRef: this.track,\n      ...this.props,\n      ...this.state\n    };\n    this.updateState(spec, setTrackStyle, () => {\n      if (this.props.autoplay) this.autoPlay(\"update\");\n      else this.pause(\"paused\");\n    });\n    // animating state should be cleared while resizing, otherwise autoplay stops working\n    this.setState({\n      animating: false\n    });\n    clearTimeout(this.animationEndCallback);\n    delete this.animationEndCallback;\n  };\n  updateState = (spec, setTrackStyle, callback) => {\n    let updatedState = initializedState(spec);\n    spec = { ...spec, ...updatedState, slideIndex: updatedState.currentSlide };\n    let targetLeft = getTrackLeft(spec);\n    spec = { ...spec, left: targetLeft };\n    let trackStyle = getTrackCSS(spec);\n    if (\n      setTrackStyle ||\n      React.Children.count(this.props.children) !==\n        React.Children.count(spec.children)\n    ) {\n      updatedState[\"trackStyle\"] = trackStyle;\n    }\n    this.setState(updatedState, callback);\n  };\n\n  ssrInit = () => {\n    if (this.props.variableWidth) {\n      let trackWidth = 0,\n        trackLeft = 0;\n      let childrenWidths = [];\n      let preClones = getPreClones({\n        ...this.props,\n        ...this.state,\n        slideCount: this.props.children.length\n      });\n      let postClones = getPostClones({\n        ...this.props,\n        ...this.state,\n        slideCount: this.props.children.length\n      });\n      this.props.children.forEach((child) => {\n        childrenWidths.push(child.props.style.width);\n        trackWidth += child.props.style.width;\n      });\n      for (let i = 0; i < preClones; i++) {\n        trackLeft += childrenWidths[childrenWidths.length - 1 - i];\n        trackWidth += childrenWidths[childrenWidths.length - 1 - i];\n      }\n      for (let i = 0; i < postClones; i++) {\n        trackWidth += childrenWidths[i];\n      }\n      for (let i = 0; i < this.state.currentSlide; i++) {\n        trackLeft += childrenWidths[i];\n      }\n      let trackStyle = {\n        width: trackWidth + \"px\",\n        left: -trackLeft + \"px\"\n      };\n      if (this.props.centerMode) {\n        let currentWidth = `${childrenWidths[this.state.currentSlide]}px`;\n        trackStyle.left = `calc(${trackStyle.left} + (100% - ${currentWidth}) / 2 ) `;\n      }\n      return {\n        trackStyle\n      };\n    }\n    let childrenCount = React.Children.count(this.props.children);\n    const spec = { ...this.props, ...this.state, slideCount: childrenCount };\n    let slideCount = getPreClones(spec) + getPostClones(spec) + childrenCount;\n    let trackWidth = (100 / this.props.slidesToShow) * slideCount;\n    let slideWidth = 100 / slideCount;\n    let trackLeft =\n      (-slideWidth *\n        (getPreClones(spec) + this.state.currentSlide) *\n        trackWidth) /\n      100;\n    if (this.props.centerMode) {\n      trackLeft += (100 - (slideWidth * trackWidth) / 100) / 2;\n    }\n    let trackStyle = {\n      width: trackWidth + \"%\",\n      left: trackLeft + \"%\"\n    };\n    return {\n      slideWidth: slideWidth + \"%\",\n      trackStyle: trackStyle\n    };\n  };\n  checkImagesLoad = () => {\n    let images =\n      (this.list &&\n        this.list.querySelectorAll &&\n        this.list.querySelectorAll(\".slick-slide img\")) ||\n      [];\n    let imagesCount = images.length,\n      loadedCount = 0;\n    Array.prototype.forEach.call(images, (image) => {\n      const handler = () =>\n        ++loadedCount && loadedCount >= imagesCount && this.onWindowResized();\n      if (!image.onclick) {\n        image.onclick = () => image.parentNode.focus();\n      } else {\n        const prevClickHandler = image.onclick;\n        image.onclick = (e) => {\n          prevClickHandler(e);\n          image.parentNode.focus();\n        };\n      }\n      if (!image.onload) {\n        if (this.props.lazyLoad) {\n          image.onload = () => {\n            this.adaptHeight();\n            this.callbackTimers.push(\n              setTimeout(this.onWindowResized, this.props.speed)\n            );\n          };\n        } else {\n          image.onload = handler;\n          image.onerror = () => {\n            handler();\n            this.props.onLazyLoadError && this.props.onLazyLoadError();\n          };\n        }\n      }\n    });\n  };\n  progressiveLazyLoad = () => {\n    let slidesToLoad = [];\n    const spec = { ...this.props, ...this.state };\n    for (\n      let index = this.state.currentSlide;\n      index < this.state.slideCount + getPostClones(spec);\n      index++\n    ) {\n      if (this.state.lazyLoadedList.indexOf(index) < 0) {\n        slidesToLoad.push(index);\n        break;\n      }\n    }\n    for (\n      let index = this.state.currentSlide - 1;\n      index >= -getPreClones(spec);\n      index--\n    ) {\n      if (this.state.lazyLoadedList.indexOf(index) < 0) {\n        slidesToLoad.push(index);\n        break;\n      }\n    }\n    if (slidesToLoad.length > 0) {\n      this.setState((state) => ({\n        lazyLoadedList: state.lazyLoadedList.concat(slidesToLoad)\n      }));\n      if (this.props.onLazyLoad) {\n        this.props.onLazyLoad(slidesToLoad);\n      }\n    } else {\n      if (this.lazyLoadTimer) {\n        clearInterval(this.lazyLoadTimer);\n        delete this.lazyLoadTimer;\n      }\n    }\n  };\n  slideHandler = (index, dontAnimate = false) => {\n    const { asNavFor, beforeChange, onLazyLoad, speed, afterChange } =\n      this.props;\n    // capture currentslide before state is updated\n    const currentSlide = this.state.currentSlide;\n    let { state, nextState } = slideHandler({\n      index,\n      ...this.props,\n      ...this.state,\n      trackRef: this.track,\n      useCSS: this.props.useCSS && !dontAnimate\n    });\n    if (!state) return;\n    beforeChange && beforeChange(currentSlide, state.currentSlide);\n    let slidesToLoad = state.lazyLoadedList.filter(\n      (value) => this.state.lazyLoadedList.indexOf(value) < 0\n    );\n    onLazyLoad && slidesToLoad.length > 0 && onLazyLoad(slidesToLoad);\n    if (!this.props.waitForAnimate && this.animationEndCallback) {\n      clearTimeout(this.animationEndCallback);\n      afterChange && afterChange(currentSlide);\n      delete this.animationEndCallback;\n    }\n    this.setState(state, () => {\n      // asNavForIndex check is to avoid recursive calls of slideHandler in waitForAnimate=false mode\n      if (asNavFor && this.asNavForIndex !== index) {\n        this.asNavForIndex = index;\n        asNavFor.innerSlider.slideHandler(index);\n      }\n      if (!nextState) return;\n      this.animationEndCallback = setTimeout(() => {\n        const { animating, ...firstBatch } = nextState;\n        this.setState(firstBatch, () => {\n          this.callbackTimers.push(\n            setTimeout(() => this.setState({ animating }), 10)\n          );\n          afterChange && afterChange(state.currentSlide);\n          delete this.animationEndCallback;\n        });\n      }, speed);\n    });\n  };\n  changeSlide = (options, dontAnimate = false) => {\n    const spec = { ...this.props, ...this.state };\n    let targetSlide = changeSlide(spec, options);\n    if (targetSlide !== 0 && !targetSlide) return;\n    if (dontAnimate === true) {\n      this.slideHandler(targetSlide, dontAnimate);\n    } else {\n      this.slideHandler(targetSlide);\n    }\n    this.props.autoplay && this.autoPlay(\"update\");\n    if (this.props.focusOnSelect) {\n      const nodes = this.list.querySelectorAll(\".slick-current\");\n      nodes[0] && nodes[0].focus();\n    }\n  };\n  clickHandler = (e) => {\n    if (this.clickable === false) {\n      e.stopPropagation();\n      e.preventDefault();\n    }\n    this.clickable = true;\n  };\n  keyHandler = (e) => {\n    let dir = keyHandler(e, this.props.accessibility, this.props.rtl);\n    dir !== \"\" && this.changeSlide({ message: dir });\n  };\n  selectHandler = (options) => {\n    this.changeSlide(options);\n  };\n  disableBodyScroll = () => {\n    const preventDefault = (e) => {\n      e = e || window.event;\n      if (e.preventDefault) e.preventDefault();\n      e.returnValue = false;\n    };\n    window.ontouchmove = preventDefault;\n  };\n  enableBodyScroll = () => {\n    window.ontouchmove = null;\n  };\n  swipeStart = (e) => {\n    if (this.props.verticalSwiping) {\n      this.disableBodyScroll();\n    }\n    let state = swipeStart(e, this.props.swipe, this.props.draggable);\n    state !== \"\" && this.setState(state);\n  };\n  swipeMove = (e) => {\n    let state = swipeMove(e, {\n      ...this.props,\n      ...this.state,\n      trackRef: this.track,\n      listRef: this.list,\n      slideIndex: this.state.currentSlide\n    });\n    if (!state) return;\n    if (state[\"swiping\"]) {\n      this.clickable = false;\n    }\n    this.setState(state);\n  };\n  swipeEnd = (e) => {\n    let state = swipeEnd(e, {\n      ...this.props,\n      ...this.state,\n      trackRef: this.track,\n      listRef: this.list,\n      slideIndex: this.state.currentSlide\n    });\n    if (!state) return;\n    let triggerSlideHandler = state[\"triggerSlideHandler\"];\n    delete state[\"triggerSlideHandler\"];\n    this.setState(state);\n    if (triggerSlideHandler === undefined) return;\n    this.slideHandler(triggerSlideHandler);\n    if (this.props.verticalSwiping) {\n      this.enableBodyScroll();\n    }\n  };\n  touchEnd = (e) => {\n    this.swipeEnd(e);\n    this.clickable = true;\n  };\n  slickPrev = () => {\n    // this and fellow methods are wrapped in setTimeout\n    // to make sure initialize setState has happened before\n    // any of such methods are called\n    this.callbackTimers.push(\n      setTimeout(() => this.changeSlide({ message: \"previous\" }), 0)\n    );\n  };\n  slickNext = () => {\n    this.callbackTimers.push(\n      setTimeout(() => this.changeSlide({ message: \"next\" }), 0)\n    );\n  };\n  slickGoTo = (slide, dontAnimate = false) => {\n    slide = Number(slide);\n    if (isNaN(slide)) return \"\";\n    this.callbackTimers.push(\n      setTimeout(\n        () =>\n          this.changeSlide(\n            {\n              message: \"index\",\n              index: slide,\n              currentSlide: this.state.currentSlide\n            },\n            dontAnimate\n          ),\n        0\n      )\n    );\n  };\n  play = () => {\n    var nextIndex;\n    if (this.props.rtl) {\n      nextIndex = this.state.currentSlide - this.props.slidesToScroll;\n    } else {\n      if (canGoNext({ ...this.props, ...this.state })) {\n        nextIndex = this.state.currentSlide + this.props.slidesToScroll;\n      } else {\n        return false;\n      }\n    }\n\n    this.slideHandler(nextIndex);\n  };\n\n  autoPlay = (playType) => {\n    if (this.autoplayTimer) {\n      clearInterval(this.autoplayTimer);\n    }\n    const autoplaying = this.state.autoplaying;\n    if (playType === \"update\") {\n      if (\n        autoplaying === \"hovered\" ||\n        autoplaying === \"focused\" ||\n        autoplaying === \"paused\"\n      ) {\n        return;\n      }\n    } else if (playType === \"leave\") {\n      if (autoplaying === \"paused\" || autoplaying === \"focused\") {\n        return;\n      }\n    } else if (playType === \"blur\") {\n      if (autoplaying === \"paused\" || autoplaying === \"hovered\") {\n        return;\n      }\n    }\n    this.autoplayTimer = setInterval(this.play, this.props.autoplaySpeed + 50);\n    this.setState({ autoplaying: \"playing\" });\n  };\n  pause = (pauseType) => {\n    if (this.autoplayTimer) {\n      clearInterval(this.autoplayTimer);\n      this.autoplayTimer = null;\n    }\n    const autoplaying = this.state.autoplaying;\n    if (pauseType === \"paused\") {\n      this.setState({ autoplaying: \"paused\" });\n    } else if (pauseType === \"focused\") {\n      if (autoplaying === \"hovered\" || autoplaying === \"playing\") {\n        this.setState({ autoplaying: \"focused\" });\n      }\n    } else {\n      // pauseType  is 'hovered'\n      if (autoplaying === \"playing\") {\n        this.setState({ autoplaying: \"hovered\" });\n      }\n    }\n  };\n  onDotsOver = () => this.props.autoplay && this.pause(\"hovered\");\n  onDotsLeave = () =>\n    this.props.autoplay &&\n    this.state.autoplaying === \"hovered\" &&\n    this.autoPlay(\"leave\");\n  onTrackOver = () => this.props.autoplay && this.pause(\"hovered\");\n  onTrackLeave = () =>\n    this.props.autoplay &&\n    this.state.autoplaying === \"hovered\" &&\n    this.autoPlay(\"leave\");\n  onSlideFocus = () => this.props.autoplay && this.pause(\"focused\");\n  onSlideBlur = () =>\n    this.props.autoplay &&\n    this.state.autoplaying === \"focused\" &&\n    this.autoPlay(\"blur\");\n\n  render = () => {\n    var className = classnames(\"slick-slider\", this.props.className, {\n      \"slick-vertical\": this.props.vertical,\n      \"slick-initialized\": true\n    });\n    let spec = { ...this.props, ...this.state };\n    let trackProps = extractObject(spec, [\n      \"fade\",\n      \"cssEase\",\n      \"speed\",\n      \"infinite\",\n      \"centerMode\",\n      \"focusOnSelect\",\n      \"currentSlide\",\n      \"lazyLoad\",\n      \"lazyLoadedList\",\n      \"rtl\",\n      \"slideWidth\",\n      \"slideHeight\",\n      \"listHeight\",\n      \"vertical\",\n      \"slidesToShow\",\n      \"slidesToScroll\",\n      \"slideCount\",\n      \"trackStyle\",\n      \"variableWidth\",\n      \"unslick\",\n      \"centerPadding\",\n      \"targetSlide\",\n      \"useCSS\"\n    ]);\n    const { pauseOnHover } = this.props;\n    trackProps = {\n      ...trackProps,\n      onMouseEnter: pauseOnHover ? this.onTrackOver : null,\n      onMouseLeave: pauseOnHover ? this.onTrackLeave : null,\n      onMouseOver: pauseOnHover ? this.onTrackOver : null,\n      focusOnSelect:\n        this.props.focusOnSelect && this.clickable ? this.selectHandler : null\n    };\n\n    var dots;\n    if (\n      this.props.dots === true &&\n      this.state.slideCount >= this.props.slidesToShow\n    ) {\n      let dotProps = extractObject(spec, [\n        \"dotsClass\",\n        \"slideCount\",\n        \"slidesToShow\",\n        \"currentSlide\",\n        \"slidesToScroll\",\n        \"clickHandler\",\n        \"children\",\n        \"customPaging\",\n        \"infinite\",\n        \"appendDots\"\n      ]);\n      const { pauseOnDotsHover } = this.props;\n      dotProps = {\n        ...dotProps,\n        clickHandler: this.changeSlide,\n        onMouseEnter: pauseOnDotsHover ? this.onDotsLeave : null,\n        onMouseOver: pauseOnDotsHover ? this.onDotsOver : null,\n        onMouseLeave: pauseOnDotsHover ? this.onDotsLeave : null\n      };\n      dots = <Dots {...dotProps} />;\n    }\n\n    var prevArrow, nextArrow;\n    let arrowProps = extractObject(spec, [\n      \"infinite\",\n      \"centerMode\",\n      \"currentSlide\",\n      \"slideCount\",\n      \"slidesToShow\",\n      \"prevArrow\",\n      \"nextArrow\"\n    ]);\n    arrowProps.clickHandler = this.changeSlide;\n\n    if (this.props.arrows) {\n      prevArrow = <PrevArrow {...arrowProps} />;\n      nextArrow = <NextArrow {...arrowProps} />;\n    }\n\n    var verticalHeightStyle = null;\n\n    if (this.props.vertical) {\n      verticalHeightStyle = {\n        height: this.state.listHeight\n      };\n    }\n\n    var centerPaddingStyle = null;\n\n    if (this.props.vertical === false) {\n      if (this.props.centerMode === true) {\n        centerPaddingStyle = {\n          padding: \"0px \" + this.props.centerPadding\n        };\n      }\n    } else {\n      if (this.props.centerMode === true) {\n        centerPaddingStyle = {\n          padding: this.props.centerPadding + \" 0px\"\n        };\n      }\n    }\n\n    const listStyle = { ...verticalHeightStyle, ...centerPaddingStyle };\n    const touchMove = this.props.touchMove;\n    let listProps = {\n      className: \"slick-list\",\n      style: listStyle,\n      onClick: this.clickHandler,\n      onMouseDown: touchMove ? this.swipeStart : null,\n      onMouseMove: this.state.dragging && touchMove ? this.swipeMove : null,\n      onMouseUp: touchMove ? this.swipeEnd : null,\n      onMouseLeave: this.state.dragging && touchMove ? this.swipeEnd : null,\n      onTouchStart: touchMove ? this.swipeStart : null,\n      onTouchMove: this.state.dragging && touchMove ? this.swipeMove : null,\n      onTouchEnd: touchMove ? this.touchEnd : null,\n      onTouchCancel: this.state.dragging && touchMove ? this.swipeEnd : null,\n      onKeyDown: this.props.accessibility ? this.keyHandler : null\n    };\n\n    let innerSliderProps = {\n      className: className,\n      dir: \"ltr\",\n      style: this.props.style\n    };\n\n    if (this.props.unslick) {\n      listProps = { className: \"slick-list\" };\n      innerSliderProps = { className, style: this.props.style };\n    }\n    return (\n      <div {...innerSliderProps}>\n        {!this.props.unslick ? prevArrow : \"\"}\n        <div ref={this.listRefHandler} {...listProps}>\n          <Track ref={this.trackRefHandler} {...trackProps}>\n            {this.props.children}\n          </Track>\n        </div>\n        {!this.props.unslick ? nextArrow : \"\"}\n        {!this.props.unslick ? dots : \"\"}\n      </div>\n    );\n  };\n}\n", "var objectWithoutPropertiesLoose = require(\"./objectWithoutPropertiesLoose.js\");\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nmodule.exports = _objectWithoutProperties, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nmodule.exports = _objectWithoutPropertiesLoose, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "const initialState = {\n  animating: false,\n  autoplaying: null,\n  currentDirection: 0,\n  currentLeft: null,\n  currentSlide: 0,\n  direction: 1,\n  dragging: false,\n  edgeDragged: false,\n  initialized: false,\n  lazyLoadedList: [],\n  listHeight: null,\n  listWidth: null,\n  scrolling: false,\n  slideCount: null,\n  slideHeight: null,\n  slideWidth: null,\n  swipeLeft: null,\n  swiped: false, // used by swipeEvent. differentites between touch and swipe.\n  swiping: false,\n  touchObject: { startX: 0, startY: 0, curX: 0, curY: 0 },\n  trackStyle: {},\n  trackWidth: 0,\n  targetSlide: 0\n};\n\nexport default initialState;\n", "/* eslint-disable no-undefined,no-param-reassign,no-shadow */\n\n/**\n * Throttle execution of a function. Especially useful for rate limiting\n * execution of handlers on events like resize and scroll.\n *\n * @param {number} delay -                  A zero-or-greater delay in milliseconds. For event callbacks, values around 100 or 250 (or even higher)\n *                                            are most useful.\n * @param {Function} callback -               A function to be executed after delay milliseconds. The `this` context and all arguments are passed through,\n *                                            as-is, to `callback` when the throttled-function is executed.\n * @param {object} [options] -              An object to configure options.\n * @param {boolean} [options.noTrailing] -   Optional, defaults to false. If noTrailing is true, callback will only execute every `delay` milliseconds\n *                                            while the throttled-function is being called. If noTrailing is false or unspecified, callback will be executed\n *                                            one final time after the last throttled-function call. (After the throttled-function has not been called for\n *                                            `delay` milliseconds, the internal counter is reset).\n * @param {boolean} [options.noLeading] -   Optional, defaults to false. If noLeading is false, the first throttled-function call will execute callback\n *                                            immediately. If noLeading is true, the first the callback execution will be skipped. It should be noted that\n *                                            callback will never executed if both noLeading = true and noTrailing = true.\n * @param {boolean} [options.debounceMode] - If `debounceMode` is true (at begin), schedule `clear` to execute after `delay` ms. If `debounceMode` is\n *                                            false (at end), schedule `callback` to execute after `delay` ms.\n *\n * @returns {Function} A new, throttled, function.\n */\nfunction throttle (delay, callback, options) {\n  var _ref = options || {},\n      _ref$noTrailing = _ref.noTrailing,\n      noTrailing = _ref$noTrailing === void 0 ? false : _ref$noTrailing,\n      _ref$noLeading = _ref.noLeading,\n      noLeading = _ref$noLeading === void 0 ? false : _ref$noLeading,\n      _ref$debounceMode = _ref.debounceMode,\n      debounceMode = _ref$debounceMode === void 0 ? undefined : _ref$debounceMode;\n  /*\n   * After wrapper has stopped being called, this timeout ensures that\n   * `callback` is executed at the proper times in `throttle` and `end`\n   * debounce modes.\n   */\n\n\n  var timeoutID;\n  var cancelled = false; // Keep track of the last time `callback` was executed.\n\n  var lastExec = 0; // Function to clear existing timeout\n\n  function clearExistingTimeout() {\n    if (timeoutID) {\n      clearTimeout(timeoutID);\n    }\n  } // Function to cancel next exec\n\n\n  function cancel(options) {\n    var _ref2 = options || {},\n        _ref2$upcomingOnly = _ref2.upcomingOnly,\n        upcomingOnly = _ref2$upcomingOnly === void 0 ? false : _ref2$upcomingOnly;\n\n    clearExistingTimeout();\n    cancelled = !upcomingOnly;\n  }\n  /*\n   * The `wrapper` function encapsulates all of the throttling / debouncing\n   * functionality and when executed will limit the rate at which `callback`\n   * is executed.\n   */\n\n\n  function wrapper() {\n    for (var _len = arguments.length, arguments_ = new Array(_len), _key = 0; _key < _len; _key++) {\n      arguments_[_key] = arguments[_key];\n    }\n\n    var self = this;\n    var elapsed = Date.now() - lastExec;\n\n    if (cancelled) {\n      return;\n    } // Execute `callback` and update the `lastExec` timestamp.\n\n\n    function exec() {\n      lastExec = Date.now();\n      callback.apply(self, arguments_);\n    }\n    /*\n     * If `debounceMode` is true (at begin) this is used to clear the flag\n     * to allow future `callback` executions.\n     */\n\n\n    function clear() {\n      timeoutID = undefined;\n    }\n\n    if (!noLeading && debounceMode && !timeoutID) {\n      /*\n       * Since `wrapper` is being called for the first time and\n       * `debounceMode` is true (at begin), execute `callback`\n       * and noLeading != true.\n       */\n      exec();\n    }\n\n    clearExistingTimeout();\n\n    if (debounceMode === undefined && elapsed > delay) {\n      if (noLeading) {\n        /*\n         * In throttle mode with noLeading, if `delay` time has\n         * been exceeded, update `lastExec` and schedule `callback`\n         * to execute after `delay` ms.\n         */\n        lastExec = Date.now();\n\n        if (!noTrailing) {\n          timeoutID = setTimeout(debounceMode ? clear : exec, delay);\n        }\n      } else {\n        /*\n         * In throttle mode without noLeading, if `delay` time has been exceeded, execute\n         * `callback`.\n         */\n        exec();\n      }\n    } else if (noTrailing !== true) {\n      /*\n       * In trailing throttle mode, since `delay` time has not been\n       * exceeded, schedule `callback` to execute `delay` ms after most\n       * recent execution.\n       *\n       * If `debounceMode` is true (at begin), schedule `clear` to execute\n       * after `delay` ms.\n       *\n       * If `debounceMode` is false (at end), schedule `callback` to\n       * execute after `delay` ms.\n       */\n      timeoutID = setTimeout(debounceMode ? clear : exec, debounceMode === undefined ? delay - elapsed : delay);\n    }\n  }\n\n  wrapper.cancel = cancel; // Return the wrapper function.\n\n  return wrapper;\n}\n\n/* eslint-disable no-undefined */\n/**\n * Debounce execution of a function. Debouncing, unlike throttling,\n * guarantees that a function is only executed a single time, either at the\n * very beginning of a series of calls, or at the very end.\n *\n * @param {number} delay -               A zero-or-greater delay in milliseconds. For event callbacks, values around 100 or 250 (or even higher) are most useful.\n * @param {Function} callback -          A function to be executed after delay milliseconds. The `this` context and all arguments are passed through, as-is,\n *                                        to `callback` when the debounced-function is executed.\n * @param {object} [options] -           An object to configure options.\n * @param {boolean} [options.atBegin] -  Optional, defaults to false. If atBegin is false or unspecified, callback will only be executed `delay` milliseconds\n *                                        after the last debounced-function call. If atBegin is true, callback will be executed only at the first debounced-function call.\n *                                        (After the throttled-function has not been called for `delay` milliseconds, the internal counter is reset).\n *\n * @returns {Function} A new, debounced function.\n */\n\nfunction debounce (delay, callback, options) {\n  var _ref = options || {},\n      _ref$atBegin = _ref.atBegin,\n      atBegin = _ref$atBegin === void 0 ? false : _ref$atBegin;\n\n  return throttle(delay, callback, {\n    debounceMode: atBegin !== false\n  });\n}\n\nexport { debounce, throttle };\n//# sourceMappingURL=index.js.map\n", "/*!\n\tCopyright (c) 2018 <PERSON>.\n\tLicensed under the MIT License (MIT), see\n\thttp://jedwatson.github.io/classnames\n*/\n/* global define */\n\n(function () {\n\t'use strict';\n\n\tvar hasOwn = {}.hasOwnProperty;\n\n\tfunction classNames () {\n\t\tvar classes = '';\n\n\t\tfor (var i = 0; i < arguments.length; i++) {\n\t\t\tvar arg = arguments[i];\n\t\t\tif (arg) {\n\t\t\t\tclasses = appendClass(classes, parseValue(arg));\n\t\t\t}\n\t\t}\n\n\t\treturn classes;\n\t}\n\n\tfunction parseValue (arg) {\n\t\tif (typeof arg === 'string' || typeof arg === 'number') {\n\t\t\treturn arg;\n\t\t}\n\n\t\tif (typeof arg !== 'object') {\n\t\t\treturn '';\n\t\t}\n\n\t\tif (Array.isArray(arg)) {\n\t\t\treturn classNames.apply(null, arg);\n\t\t}\n\n\t\tif (arg.toString !== Object.prototype.toString && !arg.toString.toString().includes('[native code]')) {\n\t\t\treturn arg.toString();\n\t\t}\n\n\t\tvar classes = '';\n\n\t\tfor (var key in arg) {\n\t\t\tif (hasOwn.call(arg, key) && arg[key]) {\n\t\t\t\tclasses = appendClass(classes, key);\n\t\t\t}\n\t\t}\n\n\t\treturn classes;\n\t}\n\n\tfunction appendClass (value, newClass) {\n\t\tif (!newClass) {\n\t\t\treturn value;\n\t\t}\n\t\n\t\tif (value) {\n\t\t\treturn value + ' ' + newClass;\n\t\t}\n\t\n\t\treturn value + newClass;\n\t}\n\n\tif (typeof module !== 'undefined' && module.exports) {\n\t\tclassNames.default = classNames;\n\t\tmodule.exports = classNames;\n\t} else if (typeof define === 'function' && typeof define.amd === 'object' && define.amd) {\n\t\t// register as 'classnames', consistent with npm package name\n\t\tdefine('classnames', [], function () {\n\t\t\treturn classNames;\n\t\t});\n\t} else {\n\t\twindow.classNames = classNames;\n\t}\n}());\n", "import React from \"react\";\nimport defaultProps from \"../default-props\";\n\nexport function clamp(number, lowerBound, upperBound) {\n  return Math.max(lowerBound, Math.min(number, upperBound));\n}\n\nexport const safePreventDefault = (event) => {\n  const passiveEvents = [\"onTouchStart\", \"onTouchMove\", \"onWheel\"];\n  if (!passiveEvents.includes(event._reactName)) {\n    event.preventDefault();\n  }\n};\n\nexport const getOnDemandLazySlides = (spec) => {\n  let onDemandSlides = [];\n  let startIndex = lazyStartIndex(spec);\n  let endIndex = lazyEndIndex(spec);\n  for (let slideIndex = startIndex; slideIndex < endIndex; slideIndex++) {\n    if (spec.lazyLoadedList.indexOf(slideIndex) < 0) {\n      onDemandSlides.push(slideIndex);\n    }\n  }\n  return onDemandSlides;\n};\n\n// return list of slides that need to be present\nexport const getRequiredLazySlides = (spec) => {\n  let requiredSlides = [];\n  let startIndex = lazyStartIndex(spec);\n  let endIndex = lazyEndIndex(spec);\n  for (let slideIndex = startIndex; slideIndex < endIndex; slideIndex++) {\n    requiredSlides.push(slideIndex);\n  }\n  return requiredSlides;\n};\n\n// startIndex that needs to be present\nexport const lazyStartIndex = (spec) =>\n  spec.currentSlide - lazySlidesOnLeft(spec);\nexport const lazyEndIndex = (spec) =>\n  spec.currentSlide + lazySlidesOnRight(spec);\nexport const lazySlidesOnLeft = (spec) =>\n  spec.centerMode\n    ? Math.floor(spec.slidesToShow / 2) +\n      (parseInt(spec.centerPadding) > 0 ? 1 : 0)\n    : 0;\nexport const lazySlidesOnRight = (spec) =>\n  spec.centerMode\n    ? Math.floor((spec.slidesToShow - 1) / 2) +\n      1 +\n      (parseInt(spec.centerPadding) > 0 ? 1 : 0)\n    : spec.slidesToShow;\n\n// get width of an element\nexport const getWidth = (elem) => (elem && elem.offsetWidth) || 0;\nexport const getHeight = (elem) => (elem && elem.offsetHeight) || 0;\nexport const getSwipeDirection = (touchObject, verticalSwiping = false) => {\n  var xDist, yDist, r, swipeAngle;\n  xDist = touchObject.startX - touchObject.curX;\n  yDist = touchObject.startY - touchObject.curY;\n  r = Math.atan2(yDist, xDist);\n  swipeAngle = Math.round((r * 180) / Math.PI);\n  if (swipeAngle < 0) {\n    swipeAngle = 360 - Math.abs(swipeAngle);\n  }\n  if (\n    (swipeAngle <= 45 && swipeAngle >= 0) ||\n    (swipeAngle <= 360 && swipeAngle >= 315)\n  ) {\n    return \"left\";\n  }\n  if (swipeAngle >= 135 && swipeAngle <= 225) {\n    return \"right\";\n  }\n  if (verticalSwiping === true) {\n    if (swipeAngle >= 35 && swipeAngle <= 135) {\n      return \"up\";\n    } else {\n      return \"down\";\n    }\n  }\n\n  return \"vertical\";\n};\n\n// whether or not we can go next\nexport const canGoNext = (spec) => {\n  let canGo = true;\n  if (!spec.infinite) {\n    if (spec.centerMode && spec.currentSlide >= spec.slideCount - 1) {\n      canGo = false;\n    } else if (\n      spec.slideCount <= spec.slidesToShow ||\n      spec.currentSlide >= spec.slideCount - spec.slidesToShow\n    ) {\n      canGo = false;\n    }\n  }\n  return canGo;\n};\n\n// given an object and a list of keys, return new object with given keys\nexport const extractObject = (spec, keys) => {\n  let newObject = {};\n  keys.forEach((key) => (newObject[key] = spec[key]));\n  return newObject;\n};\n\n// get initialized state\nexport const initializedState = (spec) => {\n  // spec also contains listRef, trackRef\n  let slideCount = React.Children.count(spec.children);\n  const listNode = spec.listRef;\n  let listWidth = Math.ceil(getWidth(listNode));\n  const trackNode = spec.trackRef && spec.trackRef.node;\n  let trackWidth = Math.ceil(getWidth(trackNode));\n  let slideWidth;\n  if (!spec.vertical) {\n    let centerPaddingAdj = spec.centerMode && parseInt(spec.centerPadding) * 2;\n    if (\n      typeof spec.centerPadding === \"string\" &&\n      spec.centerPadding.slice(-1) === \"%\"\n    ) {\n      centerPaddingAdj *= listWidth / 100;\n    }\n    slideWidth = Math.ceil((listWidth - centerPaddingAdj) / spec.slidesToShow);\n  } else {\n    slideWidth = listWidth;\n  }\n  let slideHeight =\n    listNode && getHeight(listNode.querySelector('[data-index=\"0\"]'));\n  let listHeight = slideHeight * spec.slidesToShow;\n  let currentSlide =\n    spec.currentSlide === undefined ? spec.initialSlide : spec.currentSlide;\n  if (spec.rtl && spec.currentSlide === undefined) {\n    currentSlide = slideCount - 1 - spec.initialSlide;\n  }\n  let lazyLoadedList = spec.lazyLoadedList || [];\n  let slidesToLoad = getOnDemandLazySlides({\n    ...spec,\n    currentSlide,\n    lazyLoadedList\n  });\n  lazyLoadedList = lazyLoadedList.concat(slidesToLoad);\n\n  let state = {\n    slideCount,\n    slideWidth,\n    listWidth,\n    trackWidth,\n    currentSlide,\n    slideHeight,\n    listHeight,\n    lazyLoadedList\n  };\n\n  if (spec.autoplaying === null && spec.autoplay) {\n    state[\"autoplaying\"] = \"playing\";\n  }\n\n  return state;\n};\n\nexport const slideHandler = (spec) => {\n  const {\n    waitForAnimate,\n    animating,\n    fade,\n    infinite,\n    index,\n    slideCount,\n    lazyLoad,\n    currentSlide,\n    centerMode,\n    slidesToScroll,\n    slidesToShow,\n    useCSS\n  } = spec;\n  let { lazyLoadedList } = spec;\n  if (waitForAnimate && animating) return {};\n  let animationSlide = index,\n    finalSlide,\n    animationLeft,\n    finalLeft;\n  let state = {},\n    nextState = {};\n  const targetSlide = infinite ? index : clamp(index, 0, slideCount - 1);\n  if (fade) {\n    if (!infinite && (index < 0 || index >= slideCount)) return {};\n    if (index < 0) {\n      animationSlide = index + slideCount;\n    } else if (index >= slideCount) {\n      animationSlide = index - slideCount;\n    }\n    if (lazyLoad && lazyLoadedList.indexOf(animationSlide) < 0) {\n      lazyLoadedList = lazyLoadedList.concat(animationSlide);\n    }\n    state = {\n      animating: true,\n      currentSlide: animationSlide,\n      lazyLoadedList,\n      targetSlide: animationSlide\n    };\n    nextState = { animating: false, targetSlide: animationSlide };\n  } else {\n    finalSlide = animationSlide;\n    if (animationSlide < 0) {\n      finalSlide = animationSlide + slideCount;\n      if (!infinite) finalSlide = 0;\n      else if (slideCount % slidesToScroll !== 0)\n        finalSlide = slideCount - (slideCount % slidesToScroll);\n    } else if (!canGoNext(spec) && animationSlide > currentSlide) {\n      animationSlide = finalSlide = currentSlide;\n    } else if (centerMode && animationSlide >= slideCount) {\n      animationSlide = infinite ? slideCount : slideCount - 1;\n      finalSlide = infinite ? 0 : slideCount - 1;\n    } else if (animationSlide >= slideCount) {\n      finalSlide = animationSlide - slideCount;\n      if (!infinite) finalSlide = slideCount - slidesToShow;\n      else if (slideCount % slidesToScroll !== 0) finalSlide = 0;\n    }\n\n    if (!infinite && animationSlide + slidesToShow >= slideCount) {\n      finalSlide = slideCount - slidesToShow;\n    }\n\n    animationLeft = getTrackLeft({ ...spec, slideIndex: animationSlide });\n    finalLeft = getTrackLeft({ ...spec, slideIndex: finalSlide });\n    if (!infinite) {\n      if (animationLeft === finalLeft) animationSlide = finalSlide;\n      animationLeft = finalLeft;\n    }\n    if (lazyLoad) {\n      lazyLoadedList = lazyLoadedList.concat(\n        getOnDemandLazySlides({ ...spec, currentSlide: animationSlide })\n      );\n    }\n    if (!useCSS) {\n      state = {\n        currentSlide: finalSlide,\n        trackStyle: getTrackCSS({ ...spec, left: finalLeft }),\n        lazyLoadedList,\n        targetSlide\n      };\n    } else {\n      state = {\n        animating: true,\n        currentSlide: finalSlide,\n        trackStyle: getTrackAnimateCSS({ ...spec, left: animationLeft }),\n        lazyLoadedList,\n        targetSlide\n      };\n      nextState = {\n        animating: false,\n        currentSlide: finalSlide,\n        trackStyle: getTrackCSS({ ...spec, left: finalLeft }),\n        swipeLeft: null,\n        targetSlide\n      };\n    }\n  }\n  return { state, nextState };\n};\n\nexport const changeSlide = (spec, options) => {\n  var indexOffset, previousInt, slideOffset, unevenOffset, targetSlide;\n  const {\n    slidesToScroll,\n    slidesToShow,\n    slideCount,\n    currentSlide,\n    targetSlide: previousTargetSlide,\n    lazyLoad,\n    infinite\n  } = spec;\n  unevenOffset = slideCount % slidesToScroll !== 0;\n  indexOffset = unevenOffset ? 0 : (slideCount - currentSlide) % slidesToScroll;\n  if (options.message === \"previous\") {\n    slideOffset =\n      indexOffset === 0 ? slidesToScroll : slidesToShow - indexOffset;\n    targetSlide = currentSlide - slideOffset;\n    if (lazyLoad && !infinite) {\n      previousInt = currentSlide - slideOffset;\n      targetSlide = previousInt === -1 ? slideCount - 1 : previousInt;\n    }\n    if (!infinite) {\n      targetSlide = previousTargetSlide - slidesToScroll;\n    }\n  } else if (options.message === \"next\") {\n    slideOffset = indexOffset === 0 ? slidesToScroll : indexOffset;\n    targetSlide = currentSlide + slideOffset;\n    if (lazyLoad && !infinite) {\n      targetSlide =\n        ((currentSlide + slidesToScroll) % slideCount) + indexOffset;\n    }\n    if (!infinite) {\n      targetSlide = previousTargetSlide + slidesToScroll;\n    }\n  } else if (options.message === \"dots\") {\n    // Click on dots\n    targetSlide = options.index * options.slidesToScroll;\n  } else if (options.message === \"children\") {\n    // Click on the slides\n    targetSlide = options.index;\n    if (infinite) {\n      let direction = siblingDirection({ ...spec, targetSlide });\n      if (targetSlide > options.currentSlide && direction === \"left\") {\n        targetSlide = targetSlide - slideCount;\n      } else if (targetSlide < options.currentSlide && direction === \"right\") {\n        targetSlide = targetSlide + slideCount;\n      }\n    }\n  } else if (options.message === \"index\") {\n    targetSlide = Number(options.index);\n  }\n  return targetSlide;\n};\nexport const keyHandler = (e, accessibility, rtl) => {\n  if (e.target.tagName.match(\"TEXTAREA|INPUT|SELECT\") || !accessibility)\n    return \"\";\n  if (e.keyCode === 37) return rtl ? \"next\" : \"previous\";\n  if (e.keyCode === 39) return rtl ? \"previous\" : \"next\";\n  return \"\";\n};\n\nexport const swipeStart = (e, swipe, draggable) => {\n  e.target.tagName === \"IMG\" && safePreventDefault(e);\n  if (!swipe || (!draggable && e.type.indexOf(\"mouse\") !== -1)) return \"\";\n  return {\n    dragging: true,\n    touchObject: {\n      startX: e.touches ? e.touches[0].pageX : e.clientX,\n      startY: e.touches ? e.touches[0].pageY : e.clientY,\n      curX: e.touches ? e.touches[0].pageX : e.clientX,\n      curY: e.touches ? e.touches[0].pageY : e.clientY\n    }\n  };\n};\nexport const swipeMove = (e, spec) => {\n  // spec also contains, trackRef and slideIndex\n  const {\n    scrolling,\n    animating,\n    vertical,\n    swipeToSlide,\n    verticalSwiping,\n    rtl,\n    currentSlide,\n    edgeFriction,\n    edgeDragged,\n    onEdge,\n    swiped,\n    swiping,\n    slideCount,\n    slidesToScroll,\n    infinite,\n    touchObject,\n    swipeEvent,\n    listHeight,\n    listWidth\n  } = spec;\n  if (scrolling) return;\n  if (animating) return safePreventDefault(e);\n  if (vertical && swipeToSlide && verticalSwiping) safePreventDefault(e);\n  let swipeLeft,\n    state = {};\n  let curLeft = getTrackLeft(spec);\n  touchObject.curX = e.touches ? e.touches[0].pageX : e.clientX;\n  touchObject.curY = e.touches ? e.touches[0].pageY : e.clientY;\n  touchObject.swipeLength = Math.round(\n    Math.sqrt(Math.pow(touchObject.curX - touchObject.startX, 2))\n  );\n  let verticalSwipeLength = Math.round(\n    Math.sqrt(Math.pow(touchObject.curY - touchObject.startY, 2))\n  );\n  if (!verticalSwiping && !swiping && verticalSwipeLength > 10) {\n    return { scrolling: true };\n  }\n  if (verticalSwiping) touchObject.swipeLength = verticalSwipeLength;\n  let positionOffset =\n    (!rtl ? 1 : -1) * (touchObject.curX > touchObject.startX ? 1 : -1);\n  if (verticalSwiping)\n    positionOffset = touchObject.curY > touchObject.startY ? 1 : -1;\n\n  let dotCount = Math.ceil(slideCount / slidesToScroll);\n  let swipeDirection = getSwipeDirection(spec.touchObject, verticalSwiping);\n  let touchSwipeLength = touchObject.swipeLength;\n  if (!infinite) {\n    if (\n      (currentSlide === 0 &&\n        (swipeDirection === \"right\" || swipeDirection === \"down\")) ||\n      (currentSlide + 1 >= dotCount &&\n        (swipeDirection === \"left\" || swipeDirection === \"up\")) ||\n      (!canGoNext(spec) &&\n        (swipeDirection === \"left\" || swipeDirection === \"up\"))\n    ) {\n      touchSwipeLength = touchObject.swipeLength * edgeFriction;\n      if (edgeDragged === false && onEdge) {\n        onEdge(swipeDirection);\n        state[\"edgeDragged\"] = true;\n      }\n    }\n  }\n  if (!swiped && swipeEvent) {\n    swipeEvent(swipeDirection);\n    state[\"swiped\"] = true;\n  }\n  if (!vertical) {\n    if (!rtl) {\n      swipeLeft = curLeft + touchSwipeLength * positionOffset;\n    } else {\n      swipeLeft = curLeft - touchSwipeLength * positionOffset;\n    }\n  } else {\n    swipeLeft =\n      curLeft + touchSwipeLength * (listHeight / listWidth) * positionOffset;\n  }\n  if (verticalSwiping) {\n    swipeLeft = curLeft + touchSwipeLength * positionOffset;\n  }\n  state = {\n    ...state,\n    touchObject,\n    swipeLeft,\n    trackStyle: getTrackCSS({ ...spec, left: swipeLeft })\n  };\n  if (\n    Math.abs(touchObject.curX - touchObject.startX) <\n    Math.abs(touchObject.curY - touchObject.startY) * 0.8\n  ) {\n    return state;\n  }\n  if (touchObject.swipeLength > 10) {\n    state[\"swiping\"] = true;\n    safePreventDefault(e);\n  }\n  return state;\n};\nexport const swipeEnd = (e, spec) => {\n  const {\n    dragging,\n    swipe,\n    touchObject,\n    listWidth,\n    touchThreshold,\n    verticalSwiping,\n    listHeight,\n    swipeToSlide,\n    scrolling,\n    onSwipe,\n    targetSlide,\n    currentSlide,\n    infinite\n  } = spec;\n  if (!dragging) {\n    if (swipe) safePreventDefault(e);\n    return {};\n  }\n  let minSwipe = verticalSwiping\n    ? listHeight / touchThreshold\n    : listWidth / touchThreshold;\n  let swipeDirection = getSwipeDirection(touchObject, verticalSwiping);\n  // reset the state of touch related state variables.\n  let state = {\n    dragging: false,\n    edgeDragged: false,\n    scrolling: false,\n    swiping: false,\n    swiped: false,\n    swipeLeft: null,\n    touchObject: {}\n  };\n  if (scrolling) {\n    return state;\n  }\n  if (!touchObject.swipeLength) {\n    return state;\n  }\n  if (touchObject.swipeLength > minSwipe) {\n    safePreventDefault(e);\n    if (onSwipe) {\n      onSwipe(swipeDirection);\n    }\n    let slideCount, newSlide;\n    let activeSlide = infinite ? currentSlide : targetSlide;\n    switch (swipeDirection) {\n      case \"left\":\n      case \"up\":\n        newSlide = activeSlide + getSlideCount(spec);\n        slideCount = swipeToSlide ? checkNavigable(spec, newSlide) : newSlide;\n        state[\"currentDirection\"] = 0;\n        break;\n      case \"right\":\n      case \"down\":\n        newSlide = activeSlide - getSlideCount(spec);\n        slideCount = swipeToSlide ? checkNavigable(spec, newSlide) : newSlide;\n        state[\"currentDirection\"] = 1;\n        break;\n      default:\n        slideCount = activeSlide;\n    }\n    state[\"triggerSlideHandler\"] = slideCount;\n  } else {\n    // Adjust the track back to it's original position.\n    let currentLeft = getTrackLeft(spec);\n    state[\"trackStyle\"] = getTrackAnimateCSS({ ...spec, left: currentLeft });\n  }\n  return state;\n};\nexport const getNavigableIndexes = (spec) => {\n  let max = spec.infinite ? spec.slideCount * 2 : spec.slideCount;\n  let breakpoint = spec.infinite ? spec.slidesToShow * -1 : 0;\n  let counter = spec.infinite ? spec.slidesToShow * -1 : 0;\n  let indexes = [];\n  while (breakpoint < max) {\n    indexes.push(breakpoint);\n    breakpoint = counter + spec.slidesToScroll;\n    counter += Math.min(spec.slidesToScroll, spec.slidesToShow);\n  }\n  return indexes;\n};\nexport const checkNavigable = (spec, index) => {\n  const navigables = getNavigableIndexes(spec);\n  let prevNavigable = 0;\n  if (index > navigables[navigables.length - 1]) {\n    index = navigables[navigables.length - 1];\n  } else {\n    for (let n in navigables) {\n      if (index < navigables[n]) {\n        index = prevNavigable;\n        break;\n      }\n      prevNavigable = navigables[n];\n    }\n  }\n  return index;\n};\nexport const getSlideCount = (spec) => {\n  const centerOffset = spec.centerMode\n    ? spec.slideWidth * Math.floor(spec.slidesToShow / 2)\n    : 0;\n  if (spec.swipeToSlide) {\n    let swipedSlide;\n    const slickList = spec.listRef;\n    const slides =\n      (slickList.querySelectorAll &&\n        slickList.querySelectorAll(\".slick-slide\")) ||\n      [];\n    Array.from(slides).every((slide) => {\n      if (!spec.vertical) {\n        if (\n          slide.offsetLeft - centerOffset + getWidth(slide) / 2 >\n          spec.swipeLeft * -1\n        ) {\n          swipedSlide = slide;\n          return false;\n        }\n      } else {\n        if (slide.offsetTop + getHeight(slide) / 2 > spec.swipeLeft * -1) {\n          swipedSlide = slide;\n          return false;\n        }\n      }\n\n      return true;\n    });\n\n    if (!swipedSlide) {\n      return 0;\n    }\n    const currentIndex =\n      spec.rtl === true\n        ? spec.slideCount - spec.currentSlide\n        : spec.currentSlide;\n    const slidesTraversed =\n      Math.abs(swipedSlide.dataset.index - currentIndex) || 1;\n    return slidesTraversed;\n  } else {\n    return spec.slidesToScroll;\n  }\n};\n\nexport const checkSpecKeys = (spec, keysArray) =>\n  // eslint-disable-next-line no-prototype-builtins\n  keysArray.reduce((value, key) => value && spec.hasOwnProperty(key), true)\n    ? null\n    : console.error(\"Keys Missing:\", spec);\n\nexport const getTrackCSS = (spec) => {\n  checkSpecKeys(spec, [\n    \"left\",\n    \"variableWidth\",\n    \"slideCount\",\n    \"slidesToShow\",\n    \"slideWidth\"\n  ]);\n  let trackWidth, trackHeight;\n  if (!spec.vertical) {\n    trackWidth = getTotalSlides(spec) * spec.slideWidth;\n  } else {\n    const trackChildren = spec.unslick\n      ? spec.slideCount\n      : spec.slideCount + 2 * spec.slidesToShow;\n    trackHeight = trackChildren * spec.slideHeight;\n  }\n  let style = {\n    opacity: 1,\n    transition: \"\",\n    WebkitTransition: \"\"\n  };\n  if (spec.useTransform) {\n    let WebkitTransform = !spec.vertical\n      ? \"translate3d(\" + spec.left + \"px, 0px, 0px)\"\n      : \"translate3d(0px, \" + spec.left + \"px, 0px)\";\n    let transform = !spec.vertical\n      ? \"translate3d(\" + spec.left + \"px, 0px, 0px)\"\n      : \"translate3d(0px, \" + spec.left + \"px, 0px)\";\n    let msTransform = !spec.vertical\n      ? \"translateX(\" + spec.left + \"px)\"\n      : \"translateY(\" + spec.left + \"px)\";\n    style = {\n      ...style,\n      WebkitTransform,\n      transform,\n      msTransform\n    };\n  } else {\n    if (spec.vertical) {\n      style[\"top\"] = spec.left;\n    } else {\n      style[\"left\"] = spec.left;\n    }\n  }\n  if (spec.fade) style = { opacity: 1 };\n  if (trackWidth) style.width = trackWidth;\n  if (trackHeight) style.height = trackHeight;\n\n  // Fallback for IE8\n  if (window && !window.addEventListener && window.attachEvent) {\n    if (!spec.vertical) {\n      style.marginLeft = spec.left + \"px\";\n    } else {\n      style.marginTop = spec.left + \"px\";\n    }\n  }\n\n  return style;\n};\nexport const getTrackAnimateCSS = (spec) => {\n  checkSpecKeys(spec, [\n    \"left\",\n    \"variableWidth\",\n    \"slideCount\",\n    \"slidesToShow\",\n    \"slideWidth\",\n    \"speed\",\n    \"cssEase\"\n  ]);\n  let style = getTrackCSS(spec);\n  // useCSS is true by default so it can be undefined\n  if (spec.useTransform) {\n    style.WebkitTransition =\n      \"-webkit-transform \" + spec.speed + \"ms \" + spec.cssEase;\n    style.transition = \"transform \" + spec.speed + \"ms \" + spec.cssEase;\n  } else {\n    if (spec.vertical) {\n      style.transition = \"top \" + spec.speed + \"ms \" + spec.cssEase;\n    } else {\n      style.transition = \"left \" + spec.speed + \"ms \" + spec.cssEase;\n    }\n  }\n  return style;\n};\nexport const getTrackLeft = (spec) => {\n  if (spec.unslick) {\n    return 0;\n  }\n\n  checkSpecKeys(spec, [\n    \"slideIndex\",\n    \"trackRef\",\n    \"infinite\",\n    \"centerMode\",\n    \"slideCount\",\n    \"slidesToShow\",\n    \"slidesToScroll\",\n    \"slideWidth\",\n    \"listWidth\",\n    \"variableWidth\",\n    \"slideHeight\"\n  ]);\n\n  const {\n    slideIndex,\n    trackRef,\n    infinite,\n    centerMode,\n    slideCount,\n    slidesToShow,\n    slidesToScroll,\n    slideWidth,\n    listWidth,\n    variableWidth,\n    slideHeight,\n    fade,\n    vertical\n  } = spec;\n\n  var slideOffset = 0;\n  var targetLeft;\n  var targetSlide;\n  var verticalOffset = 0;\n\n  if (fade || spec.slideCount === 1) {\n    return 0;\n  }\n\n  let slidesToOffset = 0;\n  if (infinite) {\n    slidesToOffset = -getPreClones(spec); // bring active slide to the beginning of visual area\n    // if next scroll doesn't have enough children, just reach till the end of original slides instead of shifting slidesToScroll children\n    if (\n      slideCount % slidesToScroll !== 0 &&\n      slideIndex + slidesToScroll > slideCount\n    ) {\n      slidesToOffset = -(slideIndex > slideCount\n        ? slidesToShow - (slideIndex - slideCount)\n        : slideCount % slidesToScroll);\n    }\n    // shift current slide to center of the frame\n    if (centerMode) {\n      slidesToOffset += parseInt(slidesToShow / 2);\n    }\n  } else {\n    if (\n      slideCount % slidesToScroll !== 0 &&\n      slideIndex + slidesToScroll > slideCount\n    ) {\n      slidesToOffset = slidesToShow - (slideCount % slidesToScroll);\n    }\n    if (centerMode) {\n      slidesToOffset = parseInt(slidesToShow / 2);\n    }\n  }\n  slideOffset = slidesToOffset * slideWidth;\n  verticalOffset = slidesToOffset * slideHeight;\n\n  if (!vertical) {\n    targetLeft = slideIndex * slideWidth * -1 + slideOffset;\n  } else {\n    targetLeft = slideIndex * slideHeight * -1 + verticalOffset;\n  }\n\n  if (variableWidth === true) {\n    var targetSlideIndex;\n    const trackElem = trackRef && trackRef.node;\n    targetSlideIndex = slideIndex + getPreClones(spec);\n    targetSlide = trackElem && trackElem.childNodes[targetSlideIndex];\n    targetLeft = targetSlide ? targetSlide.offsetLeft * -1 : 0;\n    if (centerMode === true) {\n      targetSlideIndex = infinite\n        ? slideIndex + getPreClones(spec)\n        : slideIndex;\n      targetSlide = trackElem && trackElem.children[targetSlideIndex];\n      targetLeft = 0;\n      for (let slide = 0; slide < targetSlideIndex; slide++) {\n        targetLeft -=\n          trackElem &&\n          trackElem.children[slide] &&\n          trackElem.children[slide].offsetWidth;\n      }\n      targetLeft -= parseInt(spec.centerPadding);\n      targetLeft += targetSlide && (listWidth - targetSlide.offsetWidth) / 2;\n    }\n  }\n\n  return targetLeft;\n};\n\nexport const getPreClones = (spec) => {\n  if (spec.unslick || !spec.infinite) {\n    return 0;\n  }\n  if (spec.variableWidth) {\n    return spec.slideCount;\n  }\n  return spec.slidesToShow + (spec.centerMode ? 1 : 0);\n};\n\nexport const getPostClones = (spec) => {\n  if (spec.unslick || !spec.infinite) {\n    return 0;\n  }\n  return spec.slideCount;\n};\n\nexport const getTotalSlides = (spec) =>\n  spec.slideCount === 1\n    ? 1\n    : getPreClones(spec) + spec.slideCount + getPostClones(spec);\nexport const siblingDirection = (spec) => {\n  if (spec.targetSlide > spec.currentSlide) {\n    if (spec.targetSlide > spec.currentSlide + slidesOnRight(spec)) {\n      return \"left\";\n    }\n    return \"right\";\n  } else {\n    if (spec.targetSlide < spec.currentSlide - slidesOnLeft(spec)) {\n      return \"right\";\n    }\n    return \"left\";\n  }\n};\n\nexport const slidesOnRight = ({\n  slidesToShow,\n  centerMode,\n  rtl,\n  centerPadding\n}) => {\n  // returns no of slides on the right of active slide\n  if (centerMode) {\n    let right = (slidesToShow - 1) / 2 + 1;\n    if (parseInt(centerPadding) > 0) right += 1;\n    if (rtl && slidesToShow % 2 === 0) right += 1;\n    return right;\n  }\n  if (rtl) {\n    return 0;\n  }\n  return slidesToShow - 1;\n};\n\nexport const slidesOnLeft = ({\n  slidesToShow,\n  centerMode,\n  rtl,\n  centerPadding\n}) => {\n  // returns no of slides on the left of active slide\n  if (centerMode) {\n    let left = (slidesToShow - 1) / 2 + 1;\n    if (parseInt(centerPadding) > 0) left += 1;\n    if (!rtl && slidesToShow % 2 === 0) left += 1;\n    return left;\n  }\n  if (rtl) {\n    return slidesToShow - 1;\n  }\n  return 0;\n};\n\nexport const canUseDOM = () =>\n  !!(\n    typeof window !== \"undefined\" &&\n    window.document &&\n    window.document.createElement\n  );\n\nexport const validSettings = Object.keys(defaultProps);\n\nexport function filterSettings(settings) {\n  return validSettings.reduce((acc, settingName) => {\n    if (settings.hasOwnProperty(settingName)) {\n      acc[settingName] = settings[settingName];\n    }\n    return acc;\n  }, {});\n}\n", "import React from \"react\";\n\nlet defaultProps = {\n  accessibility: true,\n  adaptiveHeight: false,\n  afterChange: null,\n  appendDots: (dots) => <ul style={{ display: \"block\" }}>{dots}</ul>,\n  arrows: true,\n  autoplay: false,\n  autoplaySpeed: 3000,\n  beforeChange: null,\n  centerMode: false,\n  centerPadding: \"50px\",\n  className: \"\",\n  cssEase: \"ease\",\n  customPaging: (i) => <button>{i + 1}</button>,\n  dots: false,\n  dotsClass: \"slick-dots\",\n  draggable: true,\n  easing: \"linear\",\n  edgeFriction: 0.35,\n  fade: false,\n  focusOnSelect: false,\n  infinite: true,\n  initialSlide: 0,\n  lazyLoad: null,\n  nextArrow: null,\n  onEdge: null,\n  onInit: null,\n  onLazyLoadError: null,\n  onReInit: null,\n  pauseOnDotsHover: false,\n  pauseOnFocus: false,\n  pauseOnHover: true,\n  prevArrow: null,\n  responsive: null,\n  rows: 1,\n  rtl: false,\n  slide: \"div\",\n  slidesPerRow: 1,\n  slidesToScroll: 1,\n  slidesToShow: 1,\n  speed: 500,\n  swipe: true,\n  swipeEvent: null,\n  swipeToSlide: false,\n  touchMove: true,\n  touchThreshold: 5,\n  useCSS: true,\n  useTransform: true,\n  variableWidth: false,\n  vertical: false,\n  waitForAnimate: true,\n  asNavFor: null\n};\n\nexport default defaultProps;\n", "\"use strict\";\n\nimport React from \"react\";\nimport classnames from \"classnames\";\nimport {\n  lazyStartIndex,\n  lazyEndIndex,\n  getPreClones\n} from \"./utils/innerSliderUtils\";\n\n// given specifications/props for a slide, fetch all the classes that need to be applied to the slide\nconst getSlideClasses = (spec) => {\n  let slickActive, slickCenter, slickCloned;\n  let centerOffset, index;\n\n  if (spec.rtl) {\n    index = spec.slideCount - 1 - spec.index;\n  } else {\n    index = spec.index;\n  }\n  slickCloned = index < 0 || index >= spec.slideCount;\n  if (spec.centerMode) {\n    centerOffset = Math.floor(spec.slidesToShow / 2);\n    slickCenter = (index - spec.currentSlide) % spec.slideCount === 0;\n    if (\n      index > spec.currentSlide - centerOffset - 1 &&\n      index <= spec.currentSlide + centerOffset\n    ) {\n      slickActive = true;\n    }\n  } else {\n    slickActive =\n      spec.currentSlide <= index &&\n      index < spec.currentSlide + spec.slidesToShow;\n  }\n\n  let focusedSlide;\n  if (spec.targetSlide < 0) {\n    focusedSlide = spec.targetSlide + spec.slideCount;\n  } else if (spec.targetSlide >= spec.slideCount) {\n    focusedSlide = spec.targetSlide - spec.slideCount;\n  } else {\n    focusedSlide = spec.targetSlide;\n  }\n  let slickCurrent = index === focusedSlide;\n  return {\n    \"slick-slide\": true,\n    \"slick-active\": slickActive,\n    \"slick-center\": slickCenter,\n    \"slick-cloned\": slickCloned,\n    \"slick-current\": slickCurrent // dubious in case of RTL\n  };\n};\n\nconst getSlideStyle = (spec) => {\n  let style = {};\n\n  if (spec.variableWidth === undefined || spec.variableWidth === false) {\n    style.width = spec.slideWidth;\n  }\n\n  if (spec.fade) {\n    style.position = \"relative\";\n    if (spec.vertical && spec.slideHeight) {\n      style.top = -spec.index * parseInt(spec.slideHeight);\n    } else {\n      style.left = -spec.index * parseInt(spec.slideWidth);\n    }\n    style.opacity = spec.currentSlide === spec.index ? 1 : 0;\n    style.zIndex = spec.currentSlide === spec.index ? 999 : 998;\n    if (spec.useCSS) {\n      style.transition =\n        \"opacity \" +\n        spec.speed +\n        \"ms \" +\n        spec.cssEase +\n        \", \" +\n        \"visibility \" +\n        spec.speed +\n        \"ms \" +\n        spec.cssEase;\n    }\n  }\n\n  return style;\n};\n\nconst getKey = (child, fallbackKey) => child.key + \"-\" + fallbackKey;\n\nconst renderSlides = (spec) => {\n  let key;\n  let slides = [];\n  let preCloneSlides = [];\n  let postCloneSlides = [];\n  let childrenCount = React.Children.count(spec.children);\n  let startIndex = lazyStartIndex(spec);\n  let endIndex = lazyEndIndex(spec);\n\n  React.Children.forEach(spec.children, (elem, index) => {\n    let child;\n    let childOnClickOptions = {\n      message: \"children\",\n      index: index,\n      slidesToScroll: spec.slidesToScroll,\n      currentSlide: spec.currentSlide\n    };\n\n    // in case of lazyLoad, whether or not we want to fetch the slide\n    if (\n      !spec.lazyLoad ||\n      (spec.lazyLoad && spec.lazyLoadedList.indexOf(index) >= 0)\n    ) {\n      child = elem;\n    } else {\n      child = <div />;\n    }\n    let childStyle = getSlideStyle({ ...spec, index });\n    let slideClass = child.props.className || \"\";\n    let slideClasses = getSlideClasses({ ...spec, index });\n    // push a cloned element of the desired slide\n    slides.push(\n      React.cloneElement(child, {\n        key: \"original\" + getKey(child, index),\n        \"data-index\": index,\n        className: classnames(slideClasses, slideClass),\n        tabIndex: \"-1\",\n        \"aria-hidden\": !slideClasses[\"slick-active\"],\n        style: { outline: \"none\", ...(child.props.style || {}), ...childStyle },\n        onClick: (e) => {\n          child.props && child.props.onClick && child.props.onClick(e);\n          if (spec.focusOnSelect) {\n            spec.focusOnSelect(childOnClickOptions);\n          }\n        }\n      })\n    );\n\n    // if slide needs to be precloned or postcloned\n    if (\n      spec.infinite &&\n      childrenCount > 1 &&\n      spec.fade === false &&\n      !spec.unslick\n    ) {\n      let preCloneNo = childrenCount - index;\n      if (preCloneNo <= getPreClones(spec)) {\n        key = -preCloneNo;\n        if (key >= startIndex) {\n          child = elem;\n        }\n        slideClasses = getSlideClasses({ ...spec, index: key });\n        preCloneSlides.push(\n          React.cloneElement(child, {\n            key: \"precloned\" + getKey(child, key),\n            \"data-index\": key,\n            tabIndex: \"-1\",\n            className: classnames(slideClasses, slideClass),\n            \"aria-hidden\": !slideClasses[\"slick-active\"],\n            style: { ...(child.props.style || {}), ...childStyle },\n            onClick: (e) => {\n              child.props && child.props.onClick && child.props.onClick(e);\n              if (spec.focusOnSelect) {\n                spec.focusOnSelect(childOnClickOptions);\n              }\n            }\n          })\n        );\n      }\n\n      key = childrenCount + index;\n      if (key < endIndex) {\n        child = elem;\n      }\n      slideClasses = getSlideClasses({ ...spec, index: key });\n      postCloneSlides.push(\n        React.cloneElement(child, {\n          key: \"postcloned\" + getKey(child, key),\n          \"data-index\": key,\n          tabIndex: \"-1\",\n          className: classnames(slideClasses, slideClass),\n          \"aria-hidden\": !slideClasses[\"slick-active\"],\n          style: { ...(child.props.style || {}), ...childStyle },\n          onClick: (e) => {\n            child.props && child.props.onClick && child.props.onClick(e);\n            if (spec.focusOnSelect) {\n              spec.focusOnSelect(childOnClickOptions);\n            }\n          }\n        })\n      );\n    }\n  });\n\n  if (spec.rtl) {\n    return preCloneSlides.concat(slides, postCloneSlides).reverse();\n  } else {\n    return preCloneSlides.concat(slides, postCloneSlides);\n  }\n};\n\nexport class Track extends React.PureComponent {\n  node = null;\n\n  handleRef = (ref) => {\n    this.node = ref;\n  };\n\n  render() {\n    const slides = renderSlides(this.props);\n    const { onMouseEnter, onMouseOver, onMouseLeave } = this.props;\n    const mouseEvents = { onMouseEnter, onMouseOver, onMouseLeave };\n    return (\n      <div\n        ref={this.handleRef}\n        className=\"slick-track\"\n        style={this.props.trackStyle}\n        {...mouseEvents}\n      >\n        {slides}\n      </div>\n    );\n  }\n}\n", "\"use strict\";\n\nimport React from \"react\";\nimport classnames from \"classnames\";\nimport { clamp } from \"./utils/innerSliderUtils\";\n\nconst getDotCount = spec => {\n  let dots;\n\n  if (spec.infinite) {\n    dots = Math.ceil(spec.slideCount / spec.slidesToScroll);\n  } else {\n    dots =\n      Math.ceil((spec.slideCount - spec.slidesToShow) / spec.slidesToScroll) +\n      1;\n  }\n\n  return dots;\n};\n\nexport class Dots extends React.PureComponent {\n  clickHandler(options, e) {\n    // In Autoplay the focus stays on clicked button even after transition\n    // to next slide. That only goes away by click somewhere outside\n    e.preventDefault();\n    this.props.clickHandler(options);\n  }\n  render() {\n    const {\n      onMouseEnter,\n      onMouseOver,\n      onMouseLeave,\n      infinite,\n      slidesToScroll,\n      slidesToShow,\n      slideCount,\n      currentSlide\n    } = this.props;\n    let dotCount = getDotCount({\n      slideCount,\n      slidesToScroll,\n      slidesToShow,\n      infinite\n    });\n\n    const mouseEvents = { onMouseEnter, onMouseOver, onMouseLeave };\n    let dots = [];\n    for (let i = 0; i < dotCount; i++) {\n      let _rightBound = (i + 1) * slidesToScroll - 1;\n      let rightBound = infinite\n        ? _rightBound\n        : clamp(_rightBound, 0, slideCount - 1);\n      let _leftBound = rightBound - (slidesToScroll - 1);\n      let leftBound = infinite\n        ? _leftBound\n        : clamp(_leftBound, 0, slideCount - 1);\n\n      let className = classnames({\n        \"slick-active\": infinite\n          ? currentSlide >= leftBound && currentSlide <= rightBound\n          : currentSlide === leftBound\n      });\n\n      let dotOptions = {\n        message: \"dots\",\n        index: i,\n        slidesToScroll,\n        currentSlide\n      };\n\n      let onClick = this.clickHandler.bind(this, dotOptions);\n      dots = dots.concat(\n        <li key={i} className={className}>\n          {React.cloneElement(this.props.customPaging(i), { onClick })}\n        </li>\n      );\n    }\n\n    return React.cloneElement(this.props.appendDots(dots), {\n      className: this.props.dotsClass,\n      ...mouseEvents\n    });\n  }\n}\n", "\"use strict\";\n\nimport React from \"react\";\nimport classnames from \"classnames\";\nimport { canGoNext } from \"./utils/innerSliderUtils\";\n\nexport class PrevArrow extends React.PureComponent {\n  clickHandler(options, e) {\n    if (e) {\n      e.preventDefault();\n    }\n    this.props.clickHandler(options, e);\n  }\n  render() {\n    let prevClasses = { \"slick-arrow\": true, \"slick-prev\": true };\n    let prevHandler = this.clickHandler.bind(this, { message: \"previous\" });\n\n    if (\n      !this.props.infinite &&\n      (this.props.currentSlide === 0 ||\n        this.props.slideCount <= this.props.slidesToShow)\n    ) {\n      prevClasses[\"slick-disabled\"] = true;\n      prevHandler = null;\n    }\n\n    let prevArrowProps = {\n      key: \"0\",\n      \"data-role\": \"none\",\n      className: classnames(prevClasses),\n      style: { display: \"block\" },\n      onClick: prevHandler\n    };\n    let customProps = {\n      currentSlide: this.props.currentSlide,\n      slideCount: this.props.slideCount\n    };\n    let prevArrow;\n\n    if (this.props.prevArrow) {\n      prevArrow = React.cloneElement(this.props.prevArrow, {\n        ...prevArrowProps,\n        ...customProps\n      });\n    } else {\n      prevArrow = (\n        <button key=\"0\" type=\"button\" {...prevArrowProps}>\n          {\" \"}\n          Previous\n        </button>\n      );\n    }\n\n    return prevArrow;\n  }\n}\n\nexport class NextArrow extends React.PureComponent {\n  clickHandler(options, e) {\n    if (e) {\n      e.preventDefault();\n    }\n    this.props.clickHandler(options, e);\n  }\n  render() {\n    let nextClasses = { \"slick-arrow\": true, \"slick-next\": true };\n    let nextHandler = this.clickHandler.bind(this, { message: \"next\" });\n\n    if (!canGoNext(this.props)) {\n      nextClasses[\"slick-disabled\"] = true;\n      nextHandler = null;\n    }\n\n    let nextArrowProps = {\n      key: \"1\",\n      \"data-role\": \"none\",\n      className: classnames(nextClasses),\n      style: { display: \"block\" },\n      onClick: nextHandler\n    };\n    let customProps = {\n      currentSlide: this.props.currentSlide,\n      slideCount: this.props.slideCount\n    };\n    let nextArrow;\n\n    if (this.props.nextArrow) {\n      nextArrow = React.cloneElement(this.props.nextArrow, {\n        ...nextArrowProps,\n        ...customProps\n      });\n    } else {\n      nextArrow = (\n        <button key=\"1\" type=\"button\" {...nextArrowProps}>\n          {\" \"}\n          Next\n        </button>\n      );\n    }\n\n    return nextArrow;\n  }\n}\n", "/**\r\n * A collection of shims that provide minimal functionality of the ES6 collections.\r\n *\r\n * These implementations are not meant to be used outside of the ResizeObserver\r\n * modules as they cover only a limited range of use cases.\r\n */\r\n/* eslint-disable require-jsdoc, valid-jsdoc */\r\nvar MapShim = (function () {\r\n    if (typeof Map !== 'undefined') {\r\n        return Map;\r\n    }\r\n    /**\r\n     * Returns index in provided array that matches the specified key.\r\n     *\r\n     * @param {Array<Array>} arr\r\n     * @param {*} key\r\n     * @returns {number}\r\n     */\r\n    function getIndex(arr, key) {\r\n        var result = -1;\r\n        arr.some(function (entry, index) {\r\n            if (entry[0] === key) {\r\n                result = index;\r\n                return true;\r\n            }\r\n            return false;\r\n        });\r\n        return result;\r\n    }\r\n    return /** @class */ (function () {\r\n        function class_1() {\r\n            this.__entries__ = [];\r\n        }\r\n        Object.defineProperty(class_1.prototype, \"size\", {\r\n            /**\r\n             * @returns {boolean}\r\n             */\r\n            get: function () {\r\n                return this.__entries__.length;\r\n            },\r\n            enumerable: true,\r\n            configurable: true\r\n        });\r\n        /**\r\n         * @param {*} key\r\n         * @returns {*}\r\n         */\r\n        class_1.prototype.get = function (key) {\r\n            var index = getIndex(this.__entries__, key);\r\n            var entry = this.__entries__[index];\r\n            return entry && entry[1];\r\n        };\r\n        /**\r\n         * @param {*} key\r\n         * @param {*} value\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.set = function (key, value) {\r\n            var index = getIndex(this.__entries__, key);\r\n            if (~index) {\r\n                this.__entries__[index][1] = value;\r\n            }\r\n            else {\r\n                this.__entries__.push([key, value]);\r\n            }\r\n        };\r\n        /**\r\n         * @param {*} key\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.delete = function (key) {\r\n            var entries = this.__entries__;\r\n            var index = getIndex(entries, key);\r\n            if (~index) {\r\n                entries.splice(index, 1);\r\n            }\r\n        };\r\n        /**\r\n         * @param {*} key\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.has = function (key) {\r\n            return !!~getIndex(this.__entries__, key);\r\n        };\r\n        /**\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.clear = function () {\r\n            this.__entries__.splice(0);\r\n        };\r\n        /**\r\n         * @param {Function} callback\r\n         * @param {*} [ctx=null]\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.forEach = function (callback, ctx) {\r\n            if (ctx === void 0) { ctx = null; }\r\n            for (var _i = 0, _a = this.__entries__; _i < _a.length; _i++) {\r\n                var entry = _a[_i];\r\n                callback.call(ctx, entry[1], entry[0]);\r\n            }\r\n        };\r\n        return class_1;\r\n    }());\r\n})();\n\n/**\r\n * Detects whether window and document objects are available in current environment.\r\n */\r\nvar isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined' && window.document === document;\n\n// Returns global object of a current environment.\r\nvar global$1 = (function () {\r\n    if (typeof global !== 'undefined' && global.Math === Math) {\r\n        return global;\r\n    }\r\n    if (typeof self !== 'undefined' && self.Math === Math) {\r\n        return self;\r\n    }\r\n    if (typeof window !== 'undefined' && window.Math === Math) {\r\n        return window;\r\n    }\r\n    // eslint-disable-next-line no-new-func\r\n    return Function('return this')();\r\n})();\n\n/**\r\n * A shim for the requestAnimationFrame which falls back to the setTimeout if\r\n * first one is not supported.\r\n *\r\n * @returns {number} Requests' identifier.\r\n */\r\nvar requestAnimationFrame$1 = (function () {\r\n    if (typeof requestAnimationFrame === 'function') {\r\n        // It's required to use a bounded function because IE sometimes throws\r\n        // an \"Invalid calling object\" error if rAF is invoked without the global\r\n        // object on the left hand side.\r\n        return requestAnimationFrame.bind(global$1);\r\n    }\r\n    return function (callback) { return setTimeout(function () { return callback(Date.now()); }, 1000 / 60); };\r\n})();\n\n// Defines minimum timeout before adding a trailing call.\r\nvar trailingTimeout = 2;\r\n/**\r\n * Creates a wrapper function which ensures that provided callback will be\r\n * invoked only once during the specified delay period.\r\n *\r\n * @param {Function} callback - Function to be invoked after the delay period.\r\n * @param {number} delay - Delay after which to invoke callback.\r\n * @returns {Function}\r\n */\r\nfunction throttle (callback, delay) {\r\n    var leadingCall = false, trailingCall = false, lastCallTime = 0;\r\n    /**\r\n     * Invokes the original callback function and schedules new invocation if\r\n     * the \"proxy\" was called during current request.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    function resolvePending() {\r\n        if (leadingCall) {\r\n            leadingCall = false;\r\n            callback();\r\n        }\r\n        if (trailingCall) {\r\n            proxy();\r\n        }\r\n    }\r\n    /**\r\n     * Callback invoked after the specified delay. It will further postpone\r\n     * invocation of the original function delegating it to the\r\n     * requestAnimationFrame.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    function timeoutCallback() {\r\n        requestAnimationFrame$1(resolvePending);\r\n    }\r\n    /**\r\n     * Schedules invocation of the original function.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    function proxy() {\r\n        var timeStamp = Date.now();\r\n        if (leadingCall) {\r\n            // Reject immediately following calls.\r\n            if (timeStamp - lastCallTime < trailingTimeout) {\r\n                return;\r\n            }\r\n            // Schedule new call to be in invoked when the pending one is resolved.\r\n            // This is important for \"transitions\" which never actually start\r\n            // immediately so there is a chance that we might miss one if change\r\n            // happens amids the pending invocation.\r\n            trailingCall = true;\r\n        }\r\n        else {\r\n            leadingCall = true;\r\n            trailingCall = false;\r\n            setTimeout(timeoutCallback, delay);\r\n        }\r\n        lastCallTime = timeStamp;\r\n    }\r\n    return proxy;\r\n}\n\n// Minimum delay before invoking the update of observers.\r\nvar REFRESH_DELAY = 20;\r\n// A list of substrings of CSS properties used to find transition events that\r\n// might affect dimensions of observed elements.\r\nvar transitionKeys = ['top', 'right', 'bottom', 'left', 'width', 'height', 'size', 'weight'];\r\n// Check if MutationObserver is available.\r\nvar mutationObserverSupported = typeof MutationObserver !== 'undefined';\r\n/**\r\n * Singleton controller class which handles updates of ResizeObserver instances.\r\n */\r\nvar ResizeObserverController = /** @class */ (function () {\r\n    /**\r\n     * Creates a new instance of ResizeObserverController.\r\n     *\r\n     * @private\r\n     */\r\n    function ResizeObserverController() {\r\n        /**\r\n         * Indicates whether DOM listeners have been added.\r\n         *\r\n         * @private {boolean}\r\n         */\r\n        this.connected_ = false;\r\n        /**\r\n         * Tells that controller has subscribed for Mutation Events.\r\n         *\r\n         * @private {boolean}\r\n         */\r\n        this.mutationEventsAdded_ = false;\r\n        /**\r\n         * Keeps reference to the instance of MutationObserver.\r\n         *\r\n         * @private {MutationObserver}\r\n         */\r\n        this.mutationsObserver_ = null;\r\n        /**\r\n         * A list of connected observers.\r\n         *\r\n         * @private {Array<ResizeObserverSPI>}\r\n         */\r\n        this.observers_ = [];\r\n        this.onTransitionEnd_ = this.onTransitionEnd_.bind(this);\r\n        this.refresh = throttle(this.refresh.bind(this), REFRESH_DELAY);\r\n    }\r\n    /**\r\n     * Adds observer to observers list.\r\n     *\r\n     * @param {ResizeObserverSPI} observer - Observer to be added.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.addObserver = function (observer) {\r\n        if (!~this.observers_.indexOf(observer)) {\r\n            this.observers_.push(observer);\r\n        }\r\n        // Add listeners if they haven't been added yet.\r\n        if (!this.connected_) {\r\n            this.connect_();\r\n        }\r\n    };\r\n    /**\r\n     * Removes observer from observers list.\r\n     *\r\n     * @param {ResizeObserverSPI} observer - Observer to be removed.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.removeObserver = function (observer) {\r\n        var observers = this.observers_;\r\n        var index = observers.indexOf(observer);\r\n        // Remove observer if it's present in registry.\r\n        if (~index) {\r\n            observers.splice(index, 1);\r\n        }\r\n        // Remove listeners if controller has no connected observers.\r\n        if (!observers.length && this.connected_) {\r\n            this.disconnect_();\r\n        }\r\n    };\r\n    /**\r\n     * Invokes the update of observers. It will continue running updates insofar\r\n     * it detects changes.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.refresh = function () {\r\n        var changesDetected = this.updateObservers_();\r\n        // Continue running updates if changes have been detected as there might\r\n        // be future ones caused by CSS transitions.\r\n        if (changesDetected) {\r\n            this.refresh();\r\n        }\r\n    };\r\n    /**\r\n     * Updates every observer from observers list and notifies them of queued\r\n     * entries.\r\n     *\r\n     * @private\r\n     * @returns {boolean} Returns \"true\" if any observer has detected changes in\r\n     *      dimensions of it's elements.\r\n     */\r\n    ResizeObserverController.prototype.updateObservers_ = function () {\r\n        // Collect observers that have active observations.\r\n        var activeObservers = this.observers_.filter(function (observer) {\r\n            return observer.gatherActive(), observer.hasActive();\r\n        });\r\n        // Deliver notifications in a separate cycle in order to avoid any\r\n        // collisions between observers, e.g. when multiple instances of\r\n        // ResizeObserver are tracking the same element and the callback of one\r\n        // of them changes content dimensions of the observed target. Sometimes\r\n        // this may result in notifications being blocked for the rest of observers.\r\n        activeObservers.forEach(function (observer) { return observer.broadcastActive(); });\r\n        return activeObservers.length > 0;\r\n    };\r\n    /**\r\n     * Initializes DOM listeners.\r\n     *\r\n     * @private\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.connect_ = function () {\r\n        // Do nothing if running in a non-browser environment or if listeners\r\n        // have been already added.\r\n        if (!isBrowser || this.connected_) {\r\n            return;\r\n        }\r\n        // Subscription to the \"Transitionend\" event is used as a workaround for\r\n        // delayed transitions. This way it's possible to capture at least the\r\n        // final state of an element.\r\n        document.addEventListener('transitionend', this.onTransitionEnd_);\r\n        window.addEventListener('resize', this.refresh);\r\n        if (mutationObserverSupported) {\r\n            this.mutationsObserver_ = new MutationObserver(this.refresh);\r\n            this.mutationsObserver_.observe(document, {\r\n                attributes: true,\r\n                childList: true,\r\n                characterData: true,\r\n                subtree: true\r\n            });\r\n        }\r\n        else {\r\n            document.addEventListener('DOMSubtreeModified', this.refresh);\r\n            this.mutationEventsAdded_ = true;\r\n        }\r\n        this.connected_ = true;\r\n    };\r\n    /**\r\n     * Removes DOM listeners.\r\n     *\r\n     * @private\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.disconnect_ = function () {\r\n        // Do nothing if running in a non-browser environment or if listeners\r\n        // have been already removed.\r\n        if (!isBrowser || !this.connected_) {\r\n            return;\r\n        }\r\n        document.removeEventListener('transitionend', this.onTransitionEnd_);\r\n        window.removeEventListener('resize', this.refresh);\r\n        if (this.mutationsObserver_) {\r\n            this.mutationsObserver_.disconnect();\r\n        }\r\n        if (this.mutationEventsAdded_) {\r\n            document.removeEventListener('DOMSubtreeModified', this.refresh);\r\n        }\r\n        this.mutationsObserver_ = null;\r\n        this.mutationEventsAdded_ = false;\r\n        this.connected_ = false;\r\n    };\r\n    /**\r\n     * \"Transitionend\" event handler.\r\n     *\r\n     * @private\r\n     * @param {TransitionEvent} event\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.onTransitionEnd_ = function (_a) {\r\n        var _b = _a.propertyName, propertyName = _b === void 0 ? '' : _b;\r\n        // Detect whether transition may affect dimensions of an element.\r\n        var isReflowProperty = transitionKeys.some(function (key) {\r\n            return !!~propertyName.indexOf(key);\r\n        });\r\n        if (isReflowProperty) {\r\n            this.refresh();\r\n        }\r\n    };\r\n    /**\r\n     * Returns instance of the ResizeObserverController.\r\n     *\r\n     * @returns {ResizeObserverController}\r\n     */\r\n    ResizeObserverController.getInstance = function () {\r\n        if (!this.instance_) {\r\n            this.instance_ = new ResizeObserverController();\r\n        }\r\n        return this.instance_;\r\n    };\r\n    /**\r\n     * Holds reference to the controller's instance.\r\n     *\r\n     * @private {ResizeObserverController}\r\n     */\r\n    ResizeObserverController.instance_ = null;\r\n    return ResizeObserverController;\r\n}());\n\n/**\r\n * Defines non-writable/enumerable properties of the provided target object.\r\n *\r\n * @param {Object} target - Object for which to define properties.\r\n * @param {Object} props - Properties to be defined.\r\n * @returns {Object} Target object.\r\n */\r\nvar defineConfigurable = (function (target, props) {\r\n    for (var _i = 0, _a = Object.keys(props); _i < _a.length; _i++) {\r\n        var key = _a[_i];\r\n        Object.defineProperty(target, key, {\r\n            value: props[key],\r\n            enumerable: false,\r\n            writable: false,\r\n            configurable: true\r\n        });\r\n    }\r\n    return target;\r\n});\n\n/**\r\n * Returns the global object associated with provided element.\r\n *\r\n * @param {Object} target\r\n * @returns {Object}\r\n */\r\nvar getWindowOf = (function (target) {\r\n    // Assume that the element is an instance of Node, which means that it\r\n    // has the \"ownerDocument\" property from which we can retrieve a\r\n    // corresponding global object.\r\n    var ownerGlobal = target && target.ownerDocument && target.ownerDocument.defaultView;\r\n    // Return the local global object if it's not possible extract one from\r\n    // provided element.\r\n    return ownerGlobal || global$1;\r\n});\n\n// Placeholder of an empty content rectangle.\r\nvar emptyRect = createRectInit(0, 0, 0, 0);\r\n/**\r\n * Converts provided string to a number.\r\n *\r\n * @param {number|string} value\r\n * @returns {number}\r\n */\r\nfunction toFloat(value) {\r\n    return parseFloat(value) || 0;\r\n}\r\n/**\r\n * Extracts borders size from provided styles.\r\n *\r\n * @param {CSSStyleDeclaration} styles\r\n * @param {...string} positions - Borders positions (top, right, ...)\r\n * @returns {number}\r\n */\r\nfunction getBordersSize(styles) {\r\n    var positions = [];\r\n    for (var _i = 1; _i < arguments.length; _i++) {\r\n        positions[_i - 1] = arguments[_i];\r\n    }\r\n    return positions.reduce(function (size, position) {\r\n        var value = styles['border-' + position + '-width'];\r\n        return size + toFloat(value);\r\n    }, 0);\r\n}\r\n/**\r\n * Extracts paddings sizes from provided styles.\r\n *\r\n * @param {CSSStyleDeclaration} styles\r\n * @returns {Object} Paddings box.\r\n */\r\nfunction getPaddings(styles) {\r\n    var positions = ['top', 'right', 'bottom', 'left'];\r\n    var paddings = {};\r\n    for (var _i = 0, positions_1 = positions; _i < positions_1.length; _i++) {\r\n        var position = positions_1[_i];\r\n        var value = styles['padding-' + position];\r\n        paddings[position] = toFloat(value);\r\n    }\r\n    return paddings;\r\n}\r\n/**\r\n * Calculates content rectangle of provided SVG element.\r\n *\r\n * @param {SVGGraphicsElement} target - Element content rectangle of which needs\r\n *      to be calculated.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction getSVGContentRect(target) {\r\n    var bbox = target.getBBox();\r\n    return createRectInit(0, 0, bbox.width, bbox.height);\r\n}\r\n/**\r\n * Calculates content rectangle of provided HTMLElement.\r\n *\r\n * @param {HTMLElement} target - Element for which to calculate the content rectangle.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction getHTMLElementContentRect(target) {\r\n    // Client width & height properties can't be\r\n    // used exclusively as they provide rounded values.\r\n    var clientWidth = target.clientWidth, clientHeight = target.clientHeight;\r\n    // By this condition we can catch all non-replaced inline, hidden and\r\n    // detached elements. Though elements with width & height properties less\r\n    // than 0.5 will be discarded as well.\r\n    //\r\n    // Without it we would need to implement separate methods for each of\r\n    // those cases and it's not possible to perform a precise and performance\r\n    // effective test for hidden elements. E.g. even jQuery's ':visible' filter\r\n    // gives wrong results for elements with width & height less than 0.5.\r\n    if (!clientWidth && !clientHeight) {\r\n        return emptyRect;\r\n    }\r\n    var styles = getWindowOf(target).getComputedStyle(target);\r\n    var paddings = getPaddings(styles);\r\n    var horizPad = paddings.left + paddings.right;\r\n    var vertPad = paddings.top + paddings.bottom;\r\n    // Computed styles of width & height are being used because they are the\r\n    // only dimensions available to JS that contain non-rounded values. It could\r\n    // be possible to utilize the getBoundingClientRect if only it's data wasn't\r\n    // affected by CSS transformations let alone paddings, borders and scroll bars.\r\n    var width = toFloat(styles.width), height = toFloat(styles.height);\r\n    // Width & height include paddings and borders when the 'border-box' box\r\n    // model is applied (except for IE).\r\n    if (styles.boxSizing === 'border-box') {\r\n        // Following conditions are required to handle Internet Explorer which\r\n        // doesn't include paddings and borders to computed CSS dimensions.\r\n        //\r\n        // We can say that if CSS dimensions + paddings are equal to the \"client\"\r\n        // properties then it's either IE, and thus we don't need to subtract\r\n        // anything, or an element merely doesn't have paddings/borders styles.\r\n        if (Math.round(width + horizPad) !== clientWidth) {\r\n            width -= getBordersSize(styles, 'left', 'right') + horizPad;\r\n        }\r\n        if (Math.round(height + vertPad) !== clientHeight) {\r\n            height -= getBordersSize(styles, 'top', 'bottom') + vertPad;\r\n        }\r\n    }\r\n    // Following steps can't be applied to the document's root element as its\r\n    // client[Width/Height] properties represent viewport area of the window.\r\n    // Besides, it's as well not necessary as the <html> itself neither has\r\n    // rendered scroll bars nor it can be clipped.\r\n    if (!isDocumentElement(target)) {\r\n        // In some browsers (only in Firefox, actually) CSS width & height\r\n        // include scroll bars size which can be removed at this step as scroll\r\n        // bars are the only difference between rounded dimensions + paddings\r\n        // and \"client\" properties, though that is not always true in Chrome.\r\n        var vertScrollbar = Math.round(width + horizPad) - clientWidth;\r\n        var horizScrollbar = Math.round(height + vertPad) - clientHeight;\r\n        // Chrome has a rather weird rounding of \"client\" properties.\r\n        // E.g. for an element with content width of 314.2px it sometimes gives\r\n        // the client width of 315px and for the width of 314.7px it may give\r\n        // 314px. And it doesn't happen all the time. So just ignore this delta\r\n        // as a non-relevant.\r\n        if (Math.abs(vertScrollbar) !== 1) {\r\n            width -= vertScrollbar;\r\n        }\r\n        if (Math.abs(horizScrollbar) !== 1) {\r\n            height -= horizScrollbar;\r\n        }\r\n    }\r\n    return createRectInit(paddings.left, paddings.top, width, height);\r\n}\r\n/**\r\n * Checks whether provided element is an instance of the SVGGraphicsElement.\r\n *\r\n * @param {Element} target - Element to be checked.\r\n * @returns {boolean}\r\n */\r\nvar isSVGGraphicsElement = (function () {\r\n    // Some browsers, namely IE and Edge, don't have the SVGGraphicsElement\r\n    // interface.\r\n    if (typeof SVGGraphicsElement !== 'undefined') {\r\n        return function (target) { return target instanceof getWindowOf(target).SVGGraphicsElement; };\r\n    }\r\n    // If it's so, then check that element is at least an instance of the\r\n    // SVGElement and that it has the \"getBBox\" method.\r\n    // eslint-disable-next-line no-extra-parens\r\n    return function (target) { return (target instanceof getWindowOf(target).SVGElement &&\r\n        typeof target.getBBox === 'function'); };\r\n})();\r\n/**\r\n * Checks whether provided element is a document element (<html>).\r\n *\r\n * @param {Element} target - Element to be checked.\r\n * @returns {boolean}\r\n */\r\nfunction isDocumentElement(target) {\r\n    return target === getWindowOf(target).document.documentElement;\r\n}\r\n/**\r\n * Calculates an appropriate content rectangle for provided html or svg element.\r\n *\r\n * @param {Element} target - Element content rectangle of which needs to be calculated.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction getContentRect(target) {\r\n    if (!isBrowser) {\r\n        return emptyRect;\r\n    }\r\n    if (isSVGGraphicsElement(target)) {\r\n        return getSVGContentRect(target);\r\n    }\r\n    return getHTMLElementContentRect(target);\r\n}\r\n/**\r\n * Creates rectangle with an interface of the DOMRectReadOnly.\r\n * Spec: https://drafts.fxtf.org/geometry/#domrectreadonly\r\n *\r\n * @param {DOMRectInit} rectInit - Object with rectangle's x/y coordinates and dimensions.\r\n * @returns {DOMRectReadOnly}\r\n */\r\nfunction createReadOnlyRect(_a) {\r\n    var x = _a.x, y = _a.y, width = _a.width, height = _a.height;\r\n    // If DOMRectReadOnly is available use it as a prototype for the rectangle.\r\n    var Constr = typeof DOMRectReadOnly !== 'undefined' ? DOMRectReadOnly : Object;\r\n    var rect = Object.create(Constr.prototype);\r\n    // Rectangle's properties are not writable and non-enumerable.\r\n    defineConfigurable(rect, {\r\n        x: x, y: y, width: width, height: height,\r\n        top: y,\r\n        right: x + width,\r\n        bottom: height + y,\r\n        left: x\r\n    });\r\n    return rect;\r\n}\r\n/**\r\n * Creates DOMRectInit object based on the provided dimensions and the x/y coordinates.\r\n * Spec: https://drafts.fxtf.org/geometry/#dictdef-domrectinit\r\n *\r\n * @param {number} x - X coordinate.\r\n * @param {number} y - Y coordinate.\r\n * @param {number} width - Rectangle's width.\r\n * @param {number} height - Rectangle's height.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction createRectInit(x, y, width, height) {\r\n    return { x: x, y: y, width: width, height: height };\r\n}\n\n/**\r\n * Class that is responsible for computations of the content rectangle of\r\n * provided DOM element and for keeping track of it's changes.\r\n */\r\nvar ResizeObservation = /** @class */ (function () {\r\n    /**\r\n     * Creates an instance of ResizeObservation.\r\n     *\r\n     * @param {Element} target - Element to be observed.\r\n     */\r\n    function ResizeObservation(target) {\r\n        /**\r\n         * Broadcasted width of content rectangle.\r\n         *\r\n         * @type {number}\r\n         */\r\n        this.broadcastWidth = 0;\r\n        /**\r\n         * Broadcasted height of content rectangle.\r\n         *\r\n         * @type {number}\r\n         */\r\n        this.broadcastHeight = 0;\r\n        /**\r\n         * Reference to the last observed content rectangle.\r\n         *\r\n         * @private {DOMRectInit}\r\n         */\r\n        this.contentRect_ = createRectInit(0, 0, 0, 0);\r\n        this.target = target;\r\n    }\r\n    /**\r\n     * Updates content rectangle and tells whether it's width or height properties\r\n     * have changed since the last broadcast.\r\n     *\r\n     * @returns {boolean}\r\n     */\r\n    ResizeObservation.prototype.isActive = function () {\r\n        var rect = getContentRect(this.target);\r\n        this.contentRect_ = rect;\r\n        return (rect.width !== this.broadcastWidth ||\r\n            rect.height !== this.broadcastHeight);\r\n    };\r\n    /**\r\n     * Updates 'broadcastWidth' and 'broadcastHeight' properties with a data\r\n     * from the corresponding properties of the last observed content rectangle.\r\n     *\r\n     * @returns {DOMRectInit} Last observed content rectangle.\r\n     */\r\n    ResizeObservation.prototype.broadcastRect = function () {\r\n        var rect = this.contentRect_;\r\n        this.broadcastWidth = rect.width;\r\n        this.broadcastHeight = rect.height;\r\n        return rect;\r\n    };\r\n    return ResizeObservation;\r\n}());\n\nvar ResizeObserverEntry = /** @class */ (function () {\r\n    /**\r\n     * Creates an instance of ResizeObserverEntry.\r\n     *\r\n     * @param {Element} target - Element that is being observed.\r\n     * @param {DOMRectInit} rectInit - Data of the element's content rectangle.\r\n     */\r\n    function ResizeObserverEntry(target, rectInit) {\r\n        var contentRect = createReadOnlyRect(rectInit);\r\n        // According to the specification following properties are not writable\r\n        // and are also not enumerable in the native implementation.\r\n        //\r\n        // Property accessors are not being used as they'd require to define a\r\n        // private WeakMap storage which may cause memory leaks in browsers that\r\n        // don't support this type of collections.\r\n        defineConfigurable(this, { target: target, contentRect: contentRect });\r\n    }\r\n    return ResizeObserverEntry;\r\n}());\n\nvar ResizeObserverSPI = /** @class */ (function () {\r\n    /**\r\n     * Creates a new instance of ResizeObserver.\r\n     *\r\n     * @param {ResizeObserverCallback} callback - Callback function that is invoked\r\n     *      when one of the observed elements changes it's content dimensions.\r\n     * @param {ResizeObserverController} controller - Controller instance which\r\n     *      is responsible for the updates of observer.\r\n     * @param {ResizeObserver} callbackCtx - Reference to the public\r\n     *      ResizeObserver instance which will be passed to callback function.\r\n     */\r\n    function ResizeObserverSPI(callback, controller, callbackCtx) {\r\n        /**\r\n         * Collection of resize observations that have detected changes in dimensions\r\n         * of elements.\r\n         *\r\n         * @private {Array<ResizeObservation>}\r\n         */\r\n        this.activeObservations_ = [];\r\n        /**\r\n         * Registry of the ResizeObservation instances.\r\n         *\r\n         * @private {Map<Element, ResizeObservation>}\r\n         */\r\n        this.observations_ = new MapShim();\r\n        if (typeof callback !== 'function') {\r\n            throw new TypeError('The callback provided as parameter 1 is not a function.');\r\n        }\r\n        this.callback_ = callback;\r\n        this.controller_ = controller;\r\n        this.callbackCtx_ = callbackCtx;\r\n    }\r\n    /**\r\n     * Starts observing provided element.\r\n     *\r\n     * @param {Element} target - Element to be observed.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.observe = function (target) {\r\n        if (!arguments.length) {\r\n            throw new TypeError('1 argument required, but only 0 present.');\r\n        }\r\n        // Do nothing if current environment doesn't have the Element interface.\r\n        if (typeof Element === 'undefined' || !(Element instanceof Object)) {\r\n            return;\r\n        }\r\n        if (!(target instanceof getWindowOf(target).Element)) {\r\n            throw new TypeError('parameter 1 is not of type \"Element\".');\r\n        }\r\n        var observations = this.observations_;\r\n        // Do nothing if element is already being observed.\r\n        if (observations.has(target)) {\r\n            return;\r\n        }\r\n        observations.set(target, new ResizeObservation(target));\r\n        this.controller_.addObserver(this);\r\n        // Force the update of observations.\r\n        this.controller_.refresh();\r\n    };\r\n    /**\r\n     * Stops observing provided element.\r\n     *\r\n     * @param {Element} target - Element to stop observing.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.unobserve = function (target) {\r\n        if (!arguments.length) {\r\n            throw new TypeError('1 argument required, but only 0 present.');\r\n        }\r\n        // Do nothing if current environment doesn't have the Element interface.\r\n        if (typeof Element === 'undefined' || !(Element instanceof Object)) {\r\n            return;\r\n        }\r\n        if (!(target instanceof getWindowOf(target).Element)) {\r\n            throw new TypeError('parameter 1 is not of type \"Element\".');\r\n        }\r\n        var observations = this.observations_;\r\n        // Do nothing if element is not being observed.\r\n        if (!observations.has(target)) {\r\n            return;\r\n        }\r\n        observations.delete(target);\r\n        if (!observations.size) {\r\n            this.controller_.removeObserver(this);\r\n        }\r\n    };\r\n    /**\r\n     * Stops observing all elements.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.disconnect = function () {\r\n        this.clearActive();\r\n        this.observations_.clear();\r\n        this.controller_.removeObserver(this);\r\n    };\r\n    /**\r\n     * Collects observation instances the associated element of which has changed\r\n     * it's content rectangle.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.gatherActive = function () {\r\n        var _this = this;\r\n        this.clearActive();\r\n        this.observations_.forEach(function (observation) {\r\n            if (observation.isActive()) {\r\n                _this.activeObservations_.push(observation);\r\n            }\r\n        });\r\n    };\r\n    /**\r\n     * Invokes initial callback function with a list of ResizeObserverEntry\r\n     * instances collected from active resize observations.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.broadcastActive = function () {\r\n        // Do nothing if observer doesn't have active observations.\r\n        if (!this.hasActive()) {\r\n            return;\r\n        }\r\n        var ctx = this.callbackCtx_;\r\n        // Create ResizeObserverEntry instance for every active observation.\r\n        var entries = this.activeObservations_.map(function (observation) {\r\n            return new ResizeObserverEntry(observation.target, observation.broadcastRect());\r\n        });\r\n        this.callback_.call(ctx, entries, ctx);\r\n        this.clearActive();\r\n    };\r\n    /**\r\n     * Clears the collection of active observations.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.clearActive = function () {\r\n        this.activeObservations_.splice(0);\r\n    };\r\n    /**\r\n     * Tells whether observer has active observations.\r\n     *\r\n     * @returns {boolean}\r\n     */\r\n    ResizeObserverSPI.prototype.hasActive = function () {\r\n        return this.activeObservations_.length > 0;\r\n    };\r\n    return ResizeObserverSPI;\r\n}());\n\n// Registry of internal observers. If WeakMap is not available use current shim\r\n// for the Map collection as it has all required methods and because WeakMap\r\n// can't be fully polyfilled anyway.\r\nvar observers = typeof WeakMap !== 'undefined' ? new WeakMap() : new MapShim();\r\n/**\r\n * ResizeObserver API. Encapsulates the ResizeObserver SPI implementation\r\n * exposing only those methods and properties that are defined in the spec.\r\n */\r\nvar ResizeObserver = /** @class */ (function () {\r\n    /**\r\n     * Creates a new instance of ResizeObserver.\r\n     *\r\n     * @param {ResizeObserverCallback} callback - Callback that is invoked when\r\n     *      dimensions of the observed elements change.\r\n     */\r\n    function ResizeObserver(callback) {\r\n        if (!(this instanceof ResizeObserver)) {\r\n            throw new TypeError('Cannot call a class as a function.');\r\n        }\r\n        if (!arguments.length) {\r\n            throw new TypeError('1 argument required, but only 0 present.');\r\n        }\r\n        var controller = ResizeObserverController.getInstance();\r\n        var observer = new ResizeObserverSPI(callback, controller, this);\r\n        observers.set(this, observer);\r\n    }\r\n    return ResizeObserver;\r\n}());\r\n// Expose public methods of ResizeObserver.\r\n[\r\n    'observe',\r\n    'unobserve',\r\n    'disconnect'\r\n].forEach(function (method) {\r\n    ResizeObserver.prototype[method] = function () {\r\n        var _a;\r\n        return (_a = observers.get(this))[method].apply(_a, arguments);\r\n    };\r\n});\n\nvar index = (function () {\r\n    // Export existing implementation if available.\r\n    if (typeof global$1.ResizeObserver !== 'undefined') {\r\n        return global$1.ResizeObserver;\r\n    }\r\n    return ResizeObserver;\r\n})();\n\nexport default index;\n", "var g;\n\n// This works in non-strict mode\ng = (function() {\n\treturn this;\n})();\n\ntry {\n\t// This works if eval is allowed (see CSP)\n\tg = g || new Function(\"return this\")();\n} catch (e) {\n\t// This works if the window reference is available\n\tif (typeof window === \"object\") g = window;\n}\n\n// g can still be undefined, but nothing to do about it...\n// We return undefined, instead of nothing here, so it's\n// easier to handle this case. if(!global) { ...}\n\nmodule.exports = g;\n", "var camel2hyphen = require('string-convert/camel2hyphen');\n\nvar isDimension = function (feature) {\n  var re = /[height|width]$/;\n  return re.test(feature);\n};\n\nvar obj2mq = function (obj) {\n  var mq = '';\n  var features = Object.keys(obj);\n  features.forEach(function (feature, index) {\n    var value = obj[feature];\n    feature = camel2hyphen(feature);\n    // Add px to dimension features\n    if (isDimension(feature) && typeof value === 'number') {\n      value = value + 'px';\n    }\n    if (value === true) {\n      mq += feature;\n    } else if (value === false) {\n      mq += 'not ' + feature;\n    } else {\n      mq += '(' + feature + ': ' + value + ')';\n    }\n    if (index < features.length-1) {\n      mq += ' and '\n    }\n  });\n  return mq;\n};\n\nvar json2mq = function (query) {\n  var mq = '';\n  if (typeof query === 'string') {\n    return query;\n  }\n  // Handling array of media queries\n  if (query instanceof Array) {\n    query.forEach(function (q, index) {\n      mq += obj2mq(q);\n      if (index < query.length-1) {\n        mq += ', '\n      }\n    });\n    return mq;\n  }\n  // Handling single media query\n  return obj2mq(query);\n};\n\nmodule.exports = json2mq;", "var camel2hyphen = function (str) {\n  return str\n          .replace(/[A-Z]/g, function (match) {\n            return '-' + match.toLowerCase();\n          })\n          .toLowerCase();\n};\n\nmodule.exports = camel2hyphen;"], "sourceRoot": ""}