import React, { useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { Layout } from 'antd'
import MainLayout from '@/components/layout/MainLayout'
import HomePage from '@/pages/HomePage'
import LoginPage from '@/pages/LoginPage'
import AIPlanPage from '@/pages/AIPlanPage'
import Render2DPage from '@/pages/Render2DPage'
import { useAuthStore } from '@/store/authStore'
import { useProjectStore } from '@/store/projectStore'
import { ROUTES } from '@/utils/constants'
import './App.css'

const { Content } = Layout

// 受保护的路由组件
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated } = useAuthStore()
  return isAuthenticated ? <>{children}</> : <Navigate to={ROUTES.LOGIN} replace />
}

function App() {
  const { isAuthenticated, getCurrentUser } = useAuthStore()
  const { fetchStatistics } = useProjectStore()

  useEffect(() => {
    // 应用启动时检查用户登录状态
    if (isAuthenticated) {
      getCurrentUser()
      fetchStatistics()
    }
  }, [isAuthenticated, getCurrentUser, fetchStatistics])

  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path={ROUTES.LOGIN} element={<LoginPage />} />
          <Route
            path="/*"
            element={
              <ProtectedRoute>
                <MainLayout>
                  <Routes>
                    <Route path={ROUTES.HOME} element={<HomePage />} />
                    <Route path={ROUTES.AI_PLAN} element={<AIPlanPage />} />
                    <Route path={ROUTES.RENDER_2D} element={<Render2DPage />} />
                    {/* 更多路由将在后续添加 */}
                    <Route path="*" element={<Navigate to={ROUTES.HOME} replace />} />
                  </Routes>
                </MainLayout>
              </ProtectedRoute>
            }
          />
        </Routes>
      </div>
    </Router>
  )
}

export default App
