import React from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { Layout } from 'antd'
import MainLayout from '@/components/layout/MainLayout'
import HomePage from '@/pages/HomePage'
import './App.css'

const { Content } = Layout

function App() {
  return (
    <Router>
      <div className="App">
        <MainLayout>
          <Routes>
            <Route path="/" element={<HomePage />} />
            {/* 更多路由将在后续添加 */}
          </Routes>
        </MainLayout>
      </div>
    </Router>
  )
}

export default App
