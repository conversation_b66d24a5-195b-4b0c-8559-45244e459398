import React from 'react'
import { Card, Row, Col, Stati<PERSON>, Button, Typography, Space } from 'antd'
import {
  BulbOutlined,
  PictureOutlined,
  BoxPlotOutlined,
  ToolOutlined,
  ArrowRightOutlined,
} from '@ant-design/icons'

const { Title, Paragraph } = Typography

const HomePage: React.FC = () => {
  const features = [
    {
      title: 'AI方案生成',
      description: '智能生成会展布局方案，包括展位分配、流线规划等',
      icon: <BulbOutlined style={{ fontSize: 48, color: '#1890ff' }} />,
      color: '#e6f7ff',
    },
    {
      title: '2D效果图生成',
      description: '多角度、多场景的2D效果图生成，支持实时预览',
      icon: <PictureOutlined style={{ fontSize: 48, color: '#52c41a' }} />,
      color: '#f6ffed',
    },
    {
      title: '3D模型展示',
      description: '生成3D会展模型，支持虚拟漫游和交互体验',
      icon: <BoxPlotOutlined style={{ fontSize: 48, color: '#722ed1' }} />,
      color: '#f9f0ff',
    },
    {
      title: '施工管理',
      description: '完整的施工管理流程，包括进度跟踪和质量控制',
      icon: <ToolOutlined style={{ fontSize: 48, color: '#fa8c16' }} />,
      color: '#fff7e6',
    },
  ]

  return (
    <div>
      <div style={{ marginBottom: 32 }}>
        <Title level={2}>欢迎使用会展行业虚实融合实训教学平台</Title>
        <Paragraph style={{ fontSize: 16, color: '#666' }}>
          一站式会展项目管理平台，从方案设计到施工管理，让学生体验完整的会展项目流程
        </Paragraph>
      </div>

      {/* 统计数据 */}
      <Row gutter={16} style={{ marginBottom: 32 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃项目"
              value={12}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="生成方案"
              value={48}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="效果图数量"
              value={156}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="在线用户"
              value={24}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 功能模块 */}
      <Title level={3} style={{ marginBottom: 24 }}>核心功能模块</Title>
      <Row gutter={[16, 16]}>
        {features.map((feature, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <Card
              hoverable
              style={{ 
                height: '100%',
                background: feature.color,
                border: 'none'
              }}
              bodyStyle={{ textAlign: 'center', padding: 24 }}
            >
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                {feature.icon}
                <div>
                  <Title level={4} style={{ margin: 0 }}>
                    {feature.title}
                  </Title>
                  <Paragraph style={{ margin: '8px 0 16px', color: '#666' }}>
                    {feature.description}
                  </Paragraph>
                  <Button type="primary" icon={<ArrowRightOutlined />}>
                    开始使用
                  </Button>
                </div>
              </Space>
            </Card>
          </Col>
        ))}
      </Row>

      {/* 快速开始 */}
      <Card style={{ marginTop: 32 }}>
        <Title level={4}>快速开始</Title>
        <Paragraph>
          选择一个功能模块开始您的会展项目之旅：
        </Paragraph>
        <Space wrap>
          <Button type="primary" size="large">
            创建新项目
          </Button>
          <Button size="large">
            查看教程
          </Button>
          <Button size="large">
            模板库
          </Button>
        </Space>
      </Card>
    </div>
  )
}

export default HomePage
