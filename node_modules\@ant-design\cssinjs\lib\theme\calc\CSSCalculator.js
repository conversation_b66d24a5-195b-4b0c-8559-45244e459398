"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _typeof2 = _interopRequireDefault(require("@babel/runtime/helpers/typeof"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _assertThisInitialized2 = _interopRequireDefault(require("@babel/runtime/helpers/assertThisInitialized"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _createSuper2 = _interopRequireDefault(require("@babel/runtime/helpers/createSuper"));
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _calculator = _interopRequireDefault(require("./calculator"));
var CALC_UNIT = 'CALC_UNIT';
var regexp = new RegExp(CALC_UNIT, 'g');
function unit(value) {
  if (typeof value === 'number') {
    return "".concat(value).concat(CALC_UNIT);
  }
  return value;
}
var CSSCalculator = exports.default = /*#__PURE__*/function (_AbstractCalculator) {
  (0, _inherits2.default)(CSSCalculator, _AbstractCalculator);
  var _super = (0, _createSuper2.default)(CSSCalculator);
  function CSSCalculator(num, unitlessCssVar) {
    var _this;
    (0, _classCallCheck2.default)(this, CSSCalculator);
    _this = _super.call(this);
    (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this), "result", '');
    (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this), "unitlessCssVar", void 0);
    (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this), "lowPriority", void 0);
    var numType = (0, _typeof2.default)(num);
    _this.unitlessCssVar = unitlessCssVar;
    if (num instanceof CSSCalculator) {
      _this.result = "(".concat(num.result, ")");
    } else if (numType === 'number') {
      _this.result = unit(num);
    } else if (numType === 'string') {
      _this.result = num;
    }
    return _this;
  }
  (0, _createClass2.default)(CSSCalculator, [{
    key: "add",
    value: function add(num) {
      if (num instanceof CSSCalculator) {
        this.result = "".concat(this.result, " + ").concat(num.getResult());
      } else if (typeof num === 'number' || typeof num === 'string') {
        this.result = "".concat(this.result, " + ").concat(unit(num));
      }
      this.lowPriority = true;
      return this;
    }
  }, {
    key: "sub",
    value: function sub(num) {
      if (num instanceof CSSCalculator) {
        this.result = "".concat(this.result, " - ").concat(num.getResult());
      } else if (typeof num === 'number' || typeof num === 'string') {
        this.result = "".concat(this.result, " - ").concat(unit(num));
      }
      this.lowPriority = true;
      return this;
    }
  }, {
    key: "mul",
    value: function mul(num) {
      if (this.lowPriority) {
        this.result = "(".concat(this.result, ")");
      }
      if (num instanceof CSSCalculator) {
        this.result = "".concat(this.result, " * ").concat(num.getResult(true));
      } else if (typeof num === 'number' || typeof num === 'string') {
        this.result = "".concat(this.result, " * ").concat(num);
      }
      this.lowPriority = false;
      return this;
    }
  }, {
    key: "div",
    value: function div(num) {
      if (this.lowPriority) {
        this.result = "(".concat(this.result, ")");
      }
      if (num instanceof CSSCalculator) {
        this.result = "".concat(this.result, " / ").concat(num.getResult(true));
      } else if (typeof num === 'number' || typeof num === 'string') {
        this.result = "".concat(this.result, " / ").concat(num);
      }
      this.lowPriority = false;
      return this;
    }
  }, {
    key: "getResult",
    value: function getResult(force) {
      return this.lowPriority || force ? "(".concat(this.result, ")") : this.result;
    }
  }, {
    key: "equal",
    value: function equal(options) {
      var _this2 = this;
      var _ref = options || {},
        cssUnit = _ref.unit;
      var mergedUnit = true;
      if (typeof cssUnit === 'boolean') {
        mergedUnit = cssUnit;
      } else if (Array.from(this.unitlessCssVar).some(function (cssVar) {
        return _this2.result.includes(cssVar);
      })) {
        mergedUnit = false;
      }
      this.result = this.result.replace(regexp, mergedUnit ? 'px' : '');
      if (typeof this.lowPriority !== 'undefined') {
        return "calc(".concat(this.result, ")");
      }
      return this.result;
    }
  }]);
  return CSSCalculator;
}(_calculator.default);